# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013-2014,2018-2019,2021
# <PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2023-04-24 18:40+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, "
"2013-2014,2018-2019,2021\n"
"Language-Team: French (http://www.transifex.com/django/django/language/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Humanize"
msgstr "Humanisation"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}<sup>er</sup>"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}<sup>e</sup>"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s million"
msgstr[1] "%(value)s millions"
msgstr[2] "%(value)s millions"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s milliard"
msgstr[1] "%(value)s milliards"
msgstr[2] "%(value)s milliards"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s billion"
msgstr[1] "%(value)s billions"
msgstr[2] "%(value)s billions"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s quadrillion"
msgstr[1] "%(value)s quadrillions"
msgstr[2] "%(value)s quadrillions"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s quintillion"
msgstr[1] "%(value)s quintillions"
msgstr[2] "%(value)s quintillions"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s sextillion"
msgstr[1] "%(value)s sextillion"
msgstr[2] "%(value)s sextillion"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septillion"
msgstr[1] "%(value)s septillions"
msgstr[2] "%(value)s septillions"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s octillion"
msgstr[1] "%(value)s octillions"
msgstr[2] "%(value)s octillions"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s nonillion"
msgstr[1] "%(value)s nonillions"
msgstr[2] "%(value)s nonillions"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s décillion"
msgstr[1] "%(value)s décillions"
msgstr[2] "%(value)s décillions"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s gogol"
msgstr[1] "%(value)s gogols"
msgstr[2] "%(value)s gogols"

msgid "one"
msgstr "un"

msgid "two"
msgstr "deux"

msgid "three"
msgstr "trois"

msgid "four"
msgstr "quatre"

msgid "five"
msgstr "cinq"

msgid "six"
msgstr "six"

msgid "seven"
msgstr "sept"

msgid "eight"
msgstr "huit"

msgid "nine"
msgstr "neuf"

msgid "today"
msgstr "aujourd'hui"

msgid "tomorrow"
msgstr "demain"

msgid "yesterday"
msgstr "hier"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "il y a %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "il y a une heure"
msgstr[1] "il y a %(count)s heures"
msgstr[2] "il y a %(count)s heures"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "il y a une minute"
msgstr[1] "il y a %(count)s minutes"
msgstr[2] "il y a %(count)s minutes"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "il y a une seconde"
msgstr[1] "il y a %(count)s secondes"
msgstr[2] "il y a %(count)s secondes"

msgid "now"
msgstr "maintenant"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "dans une seconde"
msgstr[1] "dans %(count)s secondes"
msgstr[2] "dans %(count)s secondes"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "dans une minute"
msgstr[1] "dans %(count)s minutes"
msgstr[2] "dans %(count)s minutes"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "dans une heure"
msgstr[1] "dans %(count)s heures"
msgstr[2] "dans %(count)s heures"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "dans %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d année"
msgstr[1] "%(num)d années"
msgstr[2] "%(num)d années"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mois"
msgstr[1] "%(num)d mois"
msgstr[2] "%(num)d mois"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d semaine"
msgstr[1] "%(num)d semaines"
msgstr[2] "%(num)d semaines"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d jour"
msgstr[1] "%(num)d jours"
msgstr[2] "%(num)d jours"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d heure"
msgstr[1] "%(num)d heures"
msgstr[2] "%(num)d heures"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minute"
msgstr[1] "%(num)d minutes"
msgstr[2] "%(num)d minutes"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d année"
msgstr[1] "%(num)d années"
msgstr[2] "%(num)d années"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mois"
msgstr[1] "%(num)d mois"
msgstr[2] "%(num)d mois"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d semaine"
msgstr[1] "%(num)d semaines"
msgstr[2] "%(num)d semaines"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d jour"
msgstr[1] "%(num)d jours"
msgstr[2] "%(num)d jours"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d heure"
msgstr[1] "%(num)d heures"
msgstr[2] "%(num)d heures"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minute"
msgstr[1] "%(num)d minutes"
msgstr[2] "%(num)d minutes"
