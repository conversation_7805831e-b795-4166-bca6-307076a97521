const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// --- 日志记录设置 (与main.cjs共享或类似逻辑) ---
// 注意：在backend.cjs中直接使用app.getPath可能不合适，因为它可能在app准备好之前被调用
// 一个更健壮的方法是从main.cjs传递logPath或log函数
// 这里为了简化，我们假设app已经ready或采用一个固定的后备路径
let logFilePath;
try {
  const userDataPath = app ? app.getPath('userData') : path.join(__dirname, 'logs'); // 后备路径
  if (app && !fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
  }
  logFilePath = path.join(userDataPath, 'backend.log');
} catch (e) {
  // 如果在测试环境或者app未初始化，使用本地日志
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  logFilePath = path.join(logsDir, 'backend.log');
  console.error('Could not get userDataPath, using local backend.log', e);
}

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;
  console.log(`(Backend Log): ${message}`);
  try {
    fs.appendFileSync(logFilePath, logMessage);
  } catch (err) {
    console.error('Failed to write to backend log file:', err);
  }
}

// 清理旧日志
try {
  if (fs.existsSync(logFilePath)) {
    fs.unlinkSync(logFilePath); 
  }
} catch (err) {
  console.error('Failed to delete old backend log file:', err);
}
log('Backend script initialized.');
// --- 日志记录设置结束 ---

const isDev = process.env.NODE_ENV === 'development';
let backendProcess = null;

function startBackend() {
  return new Promise((resolve, reject) => {
    try {
      log('Starting backend service attempt...');
      const rootDir = path.resolve(__dirname, '../../');

      const pythonExecutableName = process.platform === 'win32' ? 'python.exe' : 'python';

      // 确定Python路径 - 优先使用便携式Python环境
      let pythonPath;
      let scriptPath;
      let workingDir;

      if (isDev) {
        // 开发环境：使用虚拟环境或便携式环境
        const venvPython = path.join(rootDir, 'Backend_Django/venv/Scripts', pythonExecutableName);
        const portablePython = path.join(rootDir, 'Backend_Django/python_portable', pythonExecutableName);

        if (fs.existsSync(portablePython)) {
          pythonPath = portablePython;
          log('Using portable Python environment for development');
        } else if (fs.existsSync(venvPython)) {
          pythonPath = venvPython;
          log('Using virtual environment for development');
        } else {
          return reject(new Error('Neither portable Python nor virtual environment found in development mode'));
        }

        scriptPath = path.join(rootDir, 'Backend_Django/manage.py');
        workingDir = path.join(rootDir, 'Backend_Django');
      } else {
        // 生产环境：使用打包的便携式Python环境
        // Python.exe在Scripts目录中
        pythonPath = path.join(process.resourcesPath, 'python_portable', 'Scripts', pythonExecutableName);
        scriptPath = path.join(process.resourcesPath, 'Backend_Django', 'manage.py');
        workingDir = path.join(process.resourcesPath, 'Backend_Django');
      }

      log(`Environment: ${isDev ? 'development' : 'production'}`);
      log(`Python path: ${pythonPath}`);
      log(`Script path: ${scriptPath}`);
      log(`Working directory: ${workingDir}`);

      if (!fs.existsSync(pythonPath)) {
        const errorMsg = `Python interpreter not found at: ${pythonPath}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }
      if (!fs.existsSync(scriptPath)) {
        const errorMsg = `Django manage.py not found at: ${scriptPath}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }
      if (!fs.existsSync(workingDir)) {
        const errorMsg = `Working directory not found at: ${workingDir}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }

      log('Spawning Django process...');

      // 设置环境变量
      const processEnv = {
        ...process.env,
        PYTHONUNBUFFERED: '1',
        DJANGO_DEBUG: isDev ? 'True' : 'False',
        PYTHONPATH: workingDir,
        // 确保Python能找到便携式环境的库
        PYTHONHOME: isDev ? undefined : path.dirname(pythonPath),
        // 禁用Python用户站点目录，确保使用便携式环境
        PYTHONNOUSERSITE: '1'
      };

      // 移除undefined的环境变量
      Object.keys(processEnv).forEach(key => {
        if (processEnv[key] === undefined) {
          delete processEnv[key];
        }
      });

      log(`Environment variables: ${JSON.stringify(processEnv, null, 2)}`);

      backendProcess = spawn(pythonPath, [scriptPath, 'runserver', '8000', '--noreload'], {
        stdio: 'pipe',
        cwd: workingDir,
        env: processEnv
      });

      let isResolved = false;
      let healthCheckInterval;

      backendProcess.stdout.on('data', (data) => {
        const output = data.toString();
        log(`Backend stdout: ${output.trim()}`);

        // 检测Django服务启动的多种可能输出
        if (!isResolved && (
          output.includes('Starting development server at') ||
          output.includes('Quit the server with CTRL-BREAK') ||
          output.includes('Django version') ||
          output.includes('Watching for file changes') ||
          output.includes('Performing system checks')
        )) {
          log('Django service confirmed running.');
          isResolved = true;
          clearTimeout(startupTimeout);
          resolve();
        }
      });

      backendProcess.stderr.on('data', (data) => {
        const error = data.toString();
        log(`Backend stderr: ${error.trim()}`);
        // 不要因为警告而拒绝，除非是非常明确的错误指示
      });

      backendProcess.on('error', (error) => {
        log(`Backend process spawn error: ${error.stack || error}`);
        reject(error);
      });

      backendProcess.on('exit', (code, signal) => {
        log(`Backend process exited with code ${code} and signal ${signal}`);
        if (code !== 0 && code !== null) { // null code can happen if killed successfully
          const errorMsg = `Backend process exited unexpectedly with code ${code}.`;
          log(errorMsg); // Log it but don't necessarily reject promise here if startup was already resolved
        }
      });

      const timeout = isDev ? 60000 : 180000; // 大幅增加超时时间：开发环境60秒，生产环境180秒
      log(`Setting backend startup timeout to ${timeout / 1000} seconds.`);
      const startupTimeout = setTimeout(() => {
        if (isResolved) return; // 如果已经解决，不执行超时处理

        log(`Backend startup timeout (${timeout/1000}s). Attempting to kill process.`);
        clearInterval(healthCheckInterval); // 清理健康检查定时器

        // 检查进程是否真的在运行，或者只是挂起
        if (backendProcess && !backendProcess.killed) {
            // 尝试发送信号，如果进程未响应，可能需要更强制的杀死
            if (process.platform === 'win32') {
                spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
            } else {
                backendProcess.kill('SIGKILL'); // 更强制的信号
            }
            reject(new Error('Backend startup timed out. Process was killed.'));
        } else if (!backendProcess) {
             reject(new Error('Backend startup timed out. Process was not initiated or already exited.'));
        }
      }, timeout);

      // 添加HTTP健康检查作为备用启动检测
      healthCheckInterval = setInterval(async () => {
        if (isResolved) {
          clearInterval(healthCheckInterval);
          return;
        }

        try {
          // 尝试连接Django服务
          const http = require('http');
          const req = http.request({
            hostname: 'localhost',
            port: 8000,
            path: '/api/vision/models/',
            method: 'GET',
            timeout: 2000
          }, (res) => {
            if (!isResolved && res.statusCode) {
              log('Django service confirmed running via HTTP health check.');
              isResolved = true;
              clearTimeout(startupTimeout);
              clearInterval(healthCheckInterval);
              resolve();
            }
          });

          req.on('error', () => {
            // 忽略连接错误，继续等待
          });

          req.end();
        } catch (error) {
          // 忽略健康检查错误
        }
      }, 5000); // 每5秒检查一次

    } catch (error) {
      log(`Failed to start backend service: ${error.stack || error}`);
      reject(error);
    }
  });
}

function stopBackend() {
  return new Promise((resolve) => {
    if (backendProcess && !backendProcess.killed) {
      log('Stopping backend service...');
      if (process.platform === 'win32') {
        log(`Killing backend process tree (PID: ${backendProcess.pid}) on Windows.`);
        spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
      } else {
        log(`Sending SIGTERM to backend process (PID: ${backendProcess.pid}) on ${process.platform}.`);
        backendProcess.kill('SIGTERM');
        // Set a timeout to SIGKILL if it doesn't terminate gracefully
        setTimeout(() => {
            if (backendProcess && !backendProcess.killed) {
                log(`Backend process (PID: ${backendProcess.pid}) did not terminate with SIGTERM, sending SIGKILL.`);
                backendProcess.kill('SIGKILL');
            }
        }, 5000); // 5 seconds to terminate gracefully
      }
      backendProcess = null; // Clear immediately, actual process termination is async
      log('Backend service stop command issued.');
    }
    resolve();
  });
}

module.exports = {
  startBackend,
  stopBackend
}; 