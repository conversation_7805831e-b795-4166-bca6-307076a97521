const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// --- 日志记录设置 (与main.cjs共享或类似逻辑) ---
// 注意：在backend.cjs中直接使用app.getPath可能不合适，因为它可能在app准备好之前被调用
// 一个更健壮的方法是从main.cjs传递logPath或log函数
// 这里为了简化，我们假设app已经ready或采用一个固定的后备路径
let logFilePath;
try {
  const userDataPath = app ? app.getPath('userData') : path.join(__dirname, 'logs'); // 后备路径
  if (app && !fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
  }
  logFilePath = path.join(userDataPath, 'backend.log');
} catch (e) {
  // 如果在测试环境或者app未初始化，使用本地日志
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  logFilePath = path.join(logsDir, 'backend.log');
  console.error('Could not get userDataPath, using local backend.log', e);
}

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;
  console.log(`(Backend Log): ${message}`);
  try {
    fs.appendFileSync(logFilePath, logMessage);
  } catch (err) {
    console.error('Failed to write to backend log file:', err);
  }
}

// 清理旧日志
try {
  if (fs.existsSync(logFilePath)) {
    fs.unlinkSync(logFilePath); 
  }
} catch (err) {
  console.error('Failed to delete old backend log file:', err);
}
log('Backend script initialized.');
// --- 日志记录设置结束 ---

const isDev = process.env.NODE_ENV === 'development';
let backendProcess = null;

function startBackend() {
  log('=== startBackend function called ===');
  return new Promise((resolve, reject) => {
    try {
      log('Starting backend service attempt...');
      const rootDir = path.resolve(__dirname, '../../');

      const pythonExecutableName = process.platform === 'win32' ? 'python.exe' : 'python';

      // 确定Python路径 - 优先使用便携式Python环境
      let pythonPath;
      let scriptPath;
      let workingDir;

      if (isDev) {
        // 开发环境：使用虚拟环境
        pythonPath = path.join(rootDir, 'Backend_Django/venv/Scripts', pythonExecutableName);
        scriptPath = path.join(rootDir, 'Backend_Django/manage.py');
        workingDir = path.join(rootDir, 'Backend_Django');
        log('Using virtual environment for development');
      } else {
        // 生产环境：使用打包的虚拟环境
        pythonPath = path.join(process.resourcesPath, 'Backend_Django/venv/Scripts', pythonExecutableName);
        scriptPath = path.join(process.resourcesPath, 'Backend_Django/manage.py');
        workingDir = path.join(process.resourcesPath, 'Backend_Django');
        log('Using packaged virtual environment for production');
      }

      log(`Environment: ${isDev ? 'development' : 'production'}`);
      log(`Python path: ${pythonPath}`);
      log(`Script path: ${scriptPath}`);
      log(`Working directory: ${workingDir}`);
      log(`process.resourcesPath: ${process.resourcesPath}`);
      log(`__dirname: ${__dirname}`);

      // 检查关键文件是否存在
      log(`Python executable exists: ${fs.existsSync(pythonPath)}`);
      log(`Django script exists: ${fs.existsSync(scriptPath)}`);
      log(`Working directory exists: ${fs.existsSync(workingDir)}`);

      // 检查Python环境结构
      if (!isDev) {
        const venvDir = path.join(process.resourcesPath, 'Backend_Django/venv');
        const libDir = path.join(venvDir, 'Lib');
        const sitePackagesDir = path.join(libDir, 'site-packages');
        const encodingsDir = path.join(libDir, 'encodings');
        const pyvenvCfg = path.join(venvDir, 'pyvenv.cfg');

        log(`Venv directory exists: ${fs.existsSync(venvDir)}`);
        log(`Lib directory exists: ${fs.existsSync(libDir)}`);
        log(`Site-packages directory exists: ${fs.existsSync(sitePackagesDir)}`);
        log(`Encodings directory exists: ${fs.existsSync(encodingsDir)}`);
        log(`pyvenv.cfg exists: ${fs.existsSync(pyvenvCfg)}`);

        if (fs.existsSync(pyvenvCfg)) {
          try {
            const pyvenvContent = fs.readFileSync(pyvenvCfg, 'utf8');
            log(`pyvenv.cfg content:\n${pyvenvContent}`);
          } catch (error) {
            log(`Failed to read pyvenv.cfg: ${error.message}`);
          }
        }
      }

      if (!fs.existsSync(pythonPath)) {
        const errorMsg = `Python interpreter not found at: ${pythonPath}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }
      if (!fs.existsSync(scriptPath)) {
        const errorMsg = `Django manage.py not found at: ${scriptPath}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }
      if (!fs.existsSync(workingDir)) {
        const errorMsg = `Working directory not found at: ${workingDir}`;
        log(errorMsg);
        return reject(new Error(errorMsg));
      }

      log('Spawning Django process...');

      // 设置环境变量
      const processEnv = {
        ...process.env,
        PYTHONUNBUFFERED: '1',
        DJANGO_DEBUG: isDev ? 'True' : 'False',
        PYTHONPATH: workingDir,
        // 确保Python能找到便携式环境的库
        PYTHONHOME: isDev ? undefined : path.dirname(pythonPath),
        // 禁用Python用户站点目录，确保使用便携式环境
        PYTHONNOUSERSITE: '1'
      };

      // 移除undefined的环境变量
      Object.keys(processEnv).forEach(key => {
        if (processEnv[key] === undefined) {
          delete processEnv[key];
        }
      });

      log(`Environment variables: ${JSON.stringify(processEnv, null, 2)}`);
      log(`Spawn command: ${pythonPath} ${[scriptPath, 'runserver', '8000', '--noreload'].join(' ')}`);
      log(`Working directory: ${workingDir}`);

      // 测试Python解释器是否能正常运行
      log('Testing Python interpreter...');
      try {
        const testProcess = spawn(pythonPath, ['--version'], {
          stdio: 'pipe',
          cwd: workingDir,
          env: processEnv
        });

        testProcess.stdout.on('data', (data) => {
          log(`Python version test output: ${data.toString().trim()}`);
        });

        testProcess.stderr.on('data', (data) => {
          log(`Python version test error: ${data.toString().trim()}`);
        });

        testProcess.on('exit', (code) => {
          log(`Python version test exited with code: ${code}`);
        });
      } catch (testError) {
        log(`Python version test failed: ${testError.message}`);
      }

      // 测试Django是否能导入
      log('Testing Django import...');
      try {
        const djangoTestProcess = spawn(pythonPath, ['-c', 'import django; print("Django version:", django.VERSION)'], {
          stdio: 'pipe',
          cwd: workingDir,
          env: processEnv
        });

        djangoTestProcess.stdout.on('data', (data) => {
          log(`Django test output: ${data.toString().trim()}`);
        });

        djangoTestProcess.stderr.on('data', (data) => {
          log(`Django test error: ${data.toString().trim()}`);
        });

        djangoTestProcess.on('exit', (code) => {
          log(`Django test exited with code: ${code}`);
        });
      } catch (djangoTestError) {
        log(`Django test failed: ${djangoTestError.message}`);
      }

      backendProcess = spawn(pythonPath, [scriptPath, 'runserver', '8000', '--noreload'], {
        stdio: 'pipe',
        cwd: workingDir,
        env: processEnv
      });

      log(`Django process spawned with PID: ${backendProcess.pid}`);

      let isResolved = false;
      let healthCheckInterval;

      backendProcess.stdout.on('data', (data) => {
        const output = data.toString();
        log(`Backend stdout: ${output.trim()}`);

        // 检测Django服务启动的多种可能输出
        if (!isResolved && (
          output.includes('Starting development server at') ||
          output.includes('Quit the server with CTRL-BREAK') ||
          output.includes('Django version') ||
          output.includes('Watching for file changes') ||
          output.includes('Performing system checks') ||
          output.includes('System check identified no issues')
        )) {
          log('Django service confirmed running.');
          isResolved = true;
          clearTimeout(startupTimeout);
          clearInterval(healthCheckInterval);
          resolve();
        }
      });

      backendProcess.stderr.on('data', (data) => {
        const error = data.toString();
        log(`Backend stderr: ${error.trim()}`);

        // 检查是否有严重错误
        if (error.includes('ImportError') ||
            error.includes('ModuleNotFoundError') ||
            error.includes('SyntaxError') ||
            error.includes('Fatal') ||
            error.includes('Error:')) {
          log(`Detected serious error in backend: ${error.trim()}`);
          if (!isResolved) {
            isResolved = true;
            clearTimeout(startupTimeout);
            clearInterval(healthCheckInterval);
            reject(new Error(`Backend startup failed: ${error.trim()}`));
          }
        }
      });

      backendProcess.on('error', (error) => {
        log(`Backend process spawn error: ${error.stack || error}`);
        if (!isResolved) {
          isResolved = true;
          clearTimeout(startupTimeout);
          clearInterval(healthCheckInterval);
          reject(error);
        }
      });

      backendProcess.on('exit', (code, signal) => {
        log(`Backend process exited with code ${code} and signal ${signal}`);
        if (!isResolved && code !== 0 && code !== null) {
          isResolved = true;
          clearTimeout(startupTimeout);
          clearInterval(healthCheckInterval);
          const errorMsg = `Backend process exited unexpectedly with code ${code}.`;
          log(errorMsg);
          reject(new Error(errorMsg));
        }
      });

      const timeout = isDev ? 60000 : 180000; // 大幅增加超时时间：开发环境60秒，生产环境180秒
      log(`Setting backend startup timeout to ${timeout / 1000} seconds.`);
      const startupTimeout = setTimeout(() => {
        if (isResolved) return; // 如果已经解决，不执行超时处理

        log(`Backend startup timeout (${timeout/1000}s). Attempting to kill process.`);
        clearInterval(healthCheckInterval); // 清理健康检查定时器

        // 检查进程是否真的在运行，或者只是挂起
        if (backendProcess && !backendProcess.killed) {
            // 尝试发送信号，如果进程未响应，可能需要更强制的杀死
            if (process.platform === 'win32') {
                spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
            } else {
                backendProcess.kill('SIGKILL'); // 更强制的信号
            }
            reject(new Error('Backend startup timed out. Process was killed.'));
        } else if (!backendProcess) {
             reject(new Error('Backend startup timed out. Process was not initiated or already exited.'));
        }
      }, timeout);

      // 添加HTTP健康检查作为备用启动检测
      healthCheckInterval = setInterval(async () => {
        if (isResolved) {
          clearInterval(healthCheckInterval);
          return;
        }

        try {
          // 尝试连接Django服务
          const http = require('http');
          const req = http.request({
            hostname: 'localhost',
            port: 8000,
            path: '/api/vision/models/',
            method: 'GET',
            timeout: 2000
          }, (res) => {
            if (!isResolved && res.statusCode) {
              log('Django service confirmed running via HTTP health check.');
              isResolved = true;
              clearTimeout(startupTimeout);
              clearInterval(healthCheckInterval);
              resolve();
            }
          });

          req.on('error', () => {
            // 忽略连接错误，继续等待
          });

          req.end();
        } catch (error) {
          // 忽略健康检查错误
        }
      }, 5000); // 每5秒检查一次

    } catch (error) {
      log(`Failed to start backend service: ${error.stack || error}`);
      reject(error);
    }
  });
}

function stopBackend() {
  return new Promise((resolve) => {
    if (backendProcess && !backendProcess.killed) {
      log('Stopping backend service...');
      if (process.platform === 'win32') {
        log(`Killing backend process tree (PID: ${backendProcess.pid}) on Windows.`);
        spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
      } else {
        log(`Sending SIGTERM to backend process (PID: ${backendProcess.pid}) on ${process.platform}.`);
        backendProcess.kill('SIGTERM');
        // Set a timeout to SIGKILL if it doesn't terminate gracefully
        setTimeout(() => {
            if (backendProcess && !backendProcess.killed) {
                log(`Backend process (PID: ${backendProcess.pid}) did not terminate with SIGTERM, sending SIGKILL.`);
                backendProcess.kill('SIGKILL');
            }
        }, 5000); // 5 seconds to terminate gracefully
      }
      backendProcess = null; // Clear immediately, actual process termination is async
      log('Backend service stop command issued.');
    }
    resolve();
  });
}

module.exports = {
  startBackend,
  stopBackend
}; 