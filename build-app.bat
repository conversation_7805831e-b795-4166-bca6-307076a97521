@echo off
REM AI Vision App - Simple Build Script

echo ========================================
echo AI Vision App - Simple Build
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "Frontend\package.json" (
    echo ERROR: Please run this script from the project root directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo Step 1: Checking Python environment...
if not exist "Backend_Django\venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found at Backend_Django\venv
    echo Please create and activate the virtual environment first:
    echo   cd Backend_Django
    echo   python -m venv venv
    echo   .\venv\Scripts\activate
    echo   pip install -r requirements.txt
    pause
    exit /b 1
) else (
    echo Virtual environment found and ready
)

echo.
echo Step 2: Building frontend and packaging...
cd Frontend
call npm install
if errorlevel 1 (
    echo ERROR: npm install failed
    pause
    exit /b 1
)

call npm run electron:build:win
if errorlevel 1 (
    echo ERROR: Electron build failed
    pause
    exit /b 1
)

cd ..
echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY
echo ========================================
echo Check the Frontend\release directory for the installer
echo.

pause
