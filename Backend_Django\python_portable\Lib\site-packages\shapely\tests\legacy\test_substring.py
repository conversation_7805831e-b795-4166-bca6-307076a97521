import json
import unittest

import pytest

from shapely.errors import GeometryTypeError
from shapely.geometry import LineString, Point, shape
from shapely.ops import substring


class SubstringTestCase(unittest.TestCase):
    def setUp(self):
        self.point = Point(1, 1)
        self.line1 = LineString([(0, 0), (2, 0)])
        self.line2 = LineString([(3, 0), (3, 6), (4.5, 6)])
        self.line3 = LineString((0, i) for i in range(5))

    def test_return_startpoint(self):
        assert substring(self.line1, -500, -600).equals(Point(0, 0))
        assert substring(self.line1, -500, -500).equals(Point(0, 0))
        assert substring(self.line1, -1, -1.1, True).equals(Point(0, 0))
        assert substring(self.line1, -1.1, -1.1, True).equals(Point(0, 0))

    def test_return_endpoint(self):
        assert substring(self.line1, 500, 600).equals(Point(2, 0))
        assert substring(self.line1, 500, 500).equals(Point(2, 0))
        assert substring(self.line1, 1, 1.1, True).equals(Point(2, 0))
        assert substring(self.line1, 1.1, 1.1, True).equals(Point(2, 0))

    def test_return_midpoint(self):
        assert substring(self.line1, 0.5, 0.5).equals(Point(0.5, 0))
        assert substring(self.line1, -0.5, -0.5).equals(Point(1.5, 0))
        assert substring(self.line1, 0.5, 0.5, True).equals(Point(1, 0))
        assert substring(self.line1, -0.5, -0.5, True).equals(Point(1, 0))

        # Coming from opposite ends
        assert substring(self.line1, 1.5, -0.5).equals(Point(1.5, 0))
        assert substring(self.line1, -0.5, 1.5).equals(Point(1.5, 0))
        assert substring(self.line1, -0.7, 0.3, True).equals(Point(0.6, 0))
        assert substring(self.line1, 0.3, -0.7, True).equals(Point(0.6, 0))

    def test_return_startsubstring(self):
        assert (
            substring(self.line1, -500, 0.6).wkt == LineString([(0, 0), (0.6, 0)]).wkt
        )
        assert (
            substring(self.line1, -1.1, 0.6, True).wkt
            == LineString([(0, 0), (1.2, 0)]).wkt
        )

    def test_return_startsubstring_reversed(self):
        # not normalized
        assert substring(self.line1, -1, -500).wkt == LineString([(1, 0), (0, 0)]).wkt
        assert (
            substring(self.line3, 3.5, 0).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2), (0, 1), (0, 0)]).wkt
        )
        assert (
            substring(self.line3, -1.5, -500).wkt
            == LineString([(0, 2.5), (0, 2), (0, 1), (0, 0)]).wkt
        )
        # normalized
        assert (
            substring(self.line1, -0.5, -1.1, True).wkt
            == LineString([(1.0, 0), (0, 0)]).wkt
        )
        assert (
            substring(self.line3, 0.5, 0, True).wkt
            == LineString([(0, 2.0), (0, 1), (0, 0)]).wkt
        )
        assert (
            substring(self.line3, -0.5, -1.1, True).wkt
            == LineString([(0, 2.0), (0, 1), (0, 0)]).wkt
        )

    def test_return_endsubstring(self):
        assert substring(self.line1, 0.6, 500).wkt == LineString([(0.6, 0), (2, 0)]).wkt
        assert (
            substring(self.line1, 0.6, 1.1, True).wkt
            == LineString([(1.2, 0), (2, 0)]).wkt
        )

    def test_return_endsubstring_reversed(self):
        # not normalized
        assert substring(self.line1, 500, -1).wkt == LineString([(2, 0), (1, 0)]).wkt
        assert (
            substring(self.line3, 4, 2.5).wkt
            == LineString([(0, 4), (0, 3), (0, 2.5)]).wkt
        )
        assert (
            substring(self.line3, 500, -1.5).wkt
            == LineString([(0, 4), (0, 3), (0, 2.5)]).wkt
        )
        # normalized
        assert (
            substring(self.line1, 1.1, -0.5, True).wkt
            == LineString([(2, 0), (1.0, 0)]).wkt
        )
        assert (
            substring(self.line3, 1, 0.5, True).wkt
            == LineString([(0, 4), (0, 3), (0, 2.0)]).wkt
        )
        assert (
            substring(self.line3, 1.1, -0.5, True).wkt
            == LineString([(0, 4), (0, 3), (0, 2.0)]).wkt
        )

    def test_return_midsubstring(self):
        assert (
            substring(self.line1, 0.5, 0.6).wkt == LineString([(0.5, 0), (0.6, 0)]).wkt
        )
        assert (
            substring(self.line1, -0.6, -0.5).wkt
            == LineString([(1.4, 0), (1.5, 0)]).wkt
        )
        assert (
            substring(self.line1, 0.5, 0.6, True).wkt
            == LineString([(1, 0), (1.2, 0)]).wkt
        )
        assert (
            substring(self.line1, -0.6, -0.5, True).wkt
            == LineString([(0.8, 0), (1, 0)]).wkt
        )

    def test_return_midsubstring_reversed(self):
        assert (
            substring(self.line1, 0.6, 0.5).wkt == LineString([(0.6, 0), (0.5, 0)]).wkt
        )
        assert (
            substring(self.line1, -0.5, -0.6).wkt
            == LineString([(1.5, 0), (1.4, 0)]).wkt
        )
        assert (
            substring(self.line1, 0.6, 0.5, True).wkt
            == LineString([(1.2, 0), (1, 0)]).wkt
        )
        assert (
            substring(self.line1, -0.5, -0.6, True).wkt
            == LineString([(1, 0), (0.8, 0)]).wkt
        )

        # with vertices
        # not normalized
        assert (
            substring(self.line3, 3.5, 2.5).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )  # (+, +)
        assert (
            substring(self.line3, -0.5, -1.5).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )  # (-, -)
        assert (
            substring(self.line3, 3.5, -1.5).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )  # (+, -)
        assert (
            substring(self.line3, -0.5, 2.5).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )  # (-, +)

        # normalized
        assert (
            substring(self.line3, 0.875, 0.625, True).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )
        assert (
            substring(self.line3, -0.125, -0.375, True).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )
        assert (
            substring(self.line3, 0.875, -0.375, True).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )
        assert (
            substring(self.line3, -0.125, 0.625, True).wkt
            == LineString([(0, 3.5), (0, 3), (0, 2.5)]).wkt
        )

    def test_return_substring_with_vertices(self):
        assert (
            substring(self.line2, 1, 7).wkt == LineString([(3, 1), (3, 6), (4, 6)]).wkt
        )
        assert (
            substring(self.line2, 0.2, 0.9, True).wkt
            == LineString([(3, 1.5), (3, 6), (3.75, 6)]).wkt
        )
        assert (
            substring(self.line2, 0, 0.9, True).wkt
            == LineString([(3, 0), (3, 6), (3.75, 6)]).wkt
        )
        assert (
            substring(self.line2, 0.2, 1, True).wkt
            == LineString([(3, 1.5), (3, 6), (4.5, 6)]).wkt
        )

    def test_return_substring_issue682(self):
        assert list(substring(self.line2, 0.1, 0).coords) == [(3.0, 0.1), (3.0, 0.0)]

    def test_return_substring_issue848(self):
        line = shape(json.loads(data_issue_848))
        cut_line = substring(line, 0.7, 0.8, normalized=True)
        assert len(cut_line.coords) == 53

    def test_raise_type_error(self):
        with pytest.raises(GeometryTypeError):
            substring(Point(0, 0), 0, 0)

    def test_return_z_coord_issue1699(self):
        line_z = LineString([(0, 0, 0), (2, 0, 0)])
        assert (
            substring(line_z, 0, 0.5, True).wkt
            == LineString([(0, 0, 0), (1, 0, 0)]).wkt
        )
        assert (
            substring(line_z, 0.5, 0, True).wkt
            == LineString([(1, 0, 0), (0, 0, 0)]).wkt
        )


data_issue_848 = '{"type": "LineString", "coordinates": [[-87.71314, 41.96793], [-87.71312, 41.96667], [-87.71311, 41.96643], [-87.7131, 41.96635], [-87.71309, 41.9663], [-87.71303, 41.96621], [-87.71298, 41.96615], [-87.71292, 41.96611], [-87.7128, 41.96607], [-87.71268, 41.96605], [-87.71255, 41.96605], [-87.7124, 41.96605], [-87.71219, 41.96605], [-87.71173, 41.96606], [-87.71108, 41.96607], [-87.71027, 41.96607], [-87.70884, 41.96609], [-87.70763, 41.96611], [-87.70645, 41.96612], [-87.70399, 41.96613], [-87.70267, 41.96614], [-87.70166, 41.96615], [-87.70075, 41.96615], [-87.69954, 41.96615], [-87.69873, 41.96616], [-87.69789, 41.96618], [-87.69675, 41.9662], [-87.69502, 41.96621], [-87.69411, 41.96621], [-87.69145, 41.96623], [-87.69026, 41.96624], [-87.68946, 41.96625], [-87.6885, 41.96625], [-87.68718, 41.96628], [-87.68545, 41.96631], [-87.68399, 41.96632], [-87.68271, 41.96635], [-87.68159, 41.96636], [-87.68034, 41.96638], [-87.67863, 41.96641], [-87.67766, 41.96642], [-87.67741, 41.96641], [-87.67722, 41.9664], [-87.67695, 41.96638], [-87.67665, 41.96632], [-87.67638, 41.96623], [-87.67613, 41.96612], [-87.67589, 41.96596], [-87.6757, 41.96579], [-87.67557, 41.96565], [-87.67544, 41.96547], [-87.67539, 41.96536], [-87.6753, 41.96519], [-87.67524, 41.96503], [-87.67523, 41.96491], [-87.67522, 41.96477], [-87.67521, 41.96457], [-87.6752, 41.96434], [-87.67519, 41.96371], [-87.67517, 41.96175], [-87.67513, 41.96077], [-87.67505, 41.95798], [-87.67501, 41.95666], [-87.67497, 41.95513], [-87.67496, 41.95452], [-87.67491, 41.95392], [-87.67487, 41.95302], [-87.67485, 41.95202], [-87.67484, 41.95101], [-87.67479, 41.94959], [-87.67476, 41.94859], [-87.67474, 41.94703], [-87.67468, 41.94596], [-87.67466, 41.94513], [-87.67463, 41.94494], [-87.67457, 41.94474], [-87.6745, 41.94455], [-87.67442, 41.94438], [-87.6743, 41.94424], [-87.67419, 41.94414], [-87.67405, 41.94404], [-87.67386, 41.94393], [-87.67367, 41.94386], [-87.67348, 41.9438], [-87.67334, 41.94376], [-87.67311, 41.94373], [-87.67289, 41.9437], [-87.67263, 41.94369], [-87.67234, 41.94369], [-87.6715, 41.9437], [-87.67088, 41.94371], [-87.66938, 41.94373], [-87.66749, 41.94377], [-87.66585, 41.94378], [-87.66508, 41.94379], [-87.66361, 41.94381], [-87.6591, 41.94391], [-87.65767, 41.94391], [-87.65608, 41.94393], [-87.6555, 41.94394], [-87.65521, 41.94394], [-87.65503, 41.94393], [-87.65488, 41.9439], [-87.6547, 41.94386], [-87.65454, 41.9438], [-87.65441, 41.94375], [-87.65425, 41.94364], [-87.6541, 41.94351], [-87.654, 41.94342], [-87.65392, 41.94331], [-87.65382, 41.94319], [-87.65375, 41.94306], [-87.65367, 41.94292], [-87.65361, 41.9428], [-87.65355, 41.94269], [-87.65351, 41.94257], [-87.65347, 41.94238], [-87.65345, 41.94218], [-87.65338, 41.93975], [-87.65337, 41.93939], [-87.65337, 41.93893], [-87.65336, 41.93865], [-87.65333, 41.93763], [-87.65331, 41.93717], [-87.65328, 41.93627], [-87.65327, 41.93603], [-87.65323, 41.93532], [-87.65322, 41.93491], [-87.6532, 41.93445], [-87.65314, 41.93312], [-87.65313, 41.93273], [-87.6531, 41.93218], [-87.65307, 41.93151], [-87.65305, 41.9309], [-87.65302, 41.9303], [-87.65299, 41.92951], [-87.65296, 41.9287], [-87.65295, 41.92842], [-87.65294, 41.92768], [-87.65292, 41.92715], [-87.65289, 41.92599], [-87.65288, 41.92537], [-87.65287, 41.92505], [-87.65282, 41.92352], [-87.65276, 41.92172], [-87.65274, 41.92113], [-87.65264, 41.91822], [-87.65264, 41.91808], [-87.65262, 41.91763], [-87.65261, 41.91718], [-87.65255, 41.91563], [-87.6525, 41.91406], [-87.65242, 41.91377], [-87.65234, 41.91362], [-87.65223, 41.91351], [-87.65208, 41.91339], [-87.65183, 41.91322], [-87.65093, 41.9126], [-87.65017, 41.91203], [-87.64985, 41.9118], [-87.64971, 41.91171], [-87.64957, 41.91164], [-87.64948, 41.9116], [-87.64939, 41.91158], [-87.6492, 41.91153], [-87.649, 41.9115], [-87.64883, 41.9115], [-87.64863, 41.9115], [-87.64792, 41.91151], [-87.64781, 41.9115], [-87.64768, 41.91146], [-87.64756, 41.91139], [-87.64745, 41.91122], [-87.6474, 41.91112], [-87.64739, 41.91101], [-87.64738, 41.91086], [-87.64736, 41.91071], [-87.64734, 41.91061], [-87.64728, 41.91051], [-87.64718, 41.91044], [-87.64709, 41.9104], [-87.64697, 41.91036], [-87.64682, 41.91034], [-87.64664, 41.91033], [-87.64646, 41.91033], [-87.6458, 41.91034], [-87.64523, 41.91034], [-87.64348, 41.91036], [-87.64255, 41.91039], [-87.641, 41.9104], [-87.64038, 41.9104], [-87.63975, 41.9104], [-87.6393, 41.91041], [-87.63814, 41.91042], [-87.63798, 41.91041], [-87.63787, 41.91039], [-87.63771, 41.91034], [-87.63757, 41.91027], [-87.63746, 41.91021], [-87.63736, 41.91011], [-87.6373, 41.90999], [-87.63727, 41.90986], [-87.63726, 41.90973], [-87.63725, 41.90951], [-87.63723, 41.90874], [-87.63718, 41.90758], [-87.63713, 41.90607], [-87.63711, 41.90543], [-87.63702, 41.90381], [-87.63702, 41.90368], [-87.63701, 41.90334], [-87.63699, 41.90322], [-87.63694, 41.90312], [-87.63688, 41.90299], [-87.63682, 41.90292], [-87.63671, 41.90279], [-87.63659, 41.90265], [-87.63653, 41.90255], [-87.63649, 41.90245], [-87.63646, 41.90235], [-87.63647, 41.90221], [-87.63647, 41.90211], [-87.6365, 41.90202], [-87.63653, 41.9019], [-87.63659, 41.90177], [-87.63666, 41.90156], [-87.63669, 41.90143], [-87.6367, 41.90131], [-87.6367, 41.90119], [-87.63664, 41.90029], [-87.63664, 41.90008], [-87.63662, 41.89975], [-87.63658, 41.89892], [-87.63657, 41.89867], [-87.63654, 41.89761], [-87.63654, 41.89738], [-87.63649, 41.89726], [-87.63641, 41.89715], [-87.63634, 41.89708], [-87.63623, 41.89699], [-87.63595, 41.89677], [-87.63583, 41.89667], [-87.63574, 41.89654], [-87.63569, 41.89645], [-87.63568, 41.89633], [-87.63565, 41.89542], [-87.63563, 41.89434], [-87.6356, 41.89327], [-87.63558, 41.89261], [-87.63554, 41.89147], [-87.63553, 41.89051], [-87.63548, 41.8903], [-87.6354, 41.89021], [-87.63533, 41.89012], [-87.63524, 41.89007], [-87.63508, 41.89001], [-87.63493, 41.88997], [-87.63475, 41.88994], [-87.63462, 41.88991], [-87.63447, 41.88989], [-87.63436, 41.88984], [-87.63425, 41.88979], [-87.63414, 41.8897], [-87.63407, 41.88962], [-87.63402, 41.88952], [-87.63399, 41.88943], [-87.63397, 41.88897], [-87.63396, 41.88707], [-87.63391, 41.88572], [-87.63389, 41.88441], [-87.63385, 41.8827], [-87.63384, 41.88144], [-87.63378, 41.88014], [-87.63374, 41.87872], [-87.63369, 41.87726], [-87.63369, 41.87706], [-87.63365, 41.87695], [-87.63359, 41.87691], [-87.63353, 41.87688], [-87.63345, 41.87686], [-87.63338, 41.87685], [-87.63263, 41.87685], [-87.63173, 41.87686], [-87.62925, 41.87689], [-87.62821, 41.87691], [-87.62757, 41.87693], [-87.6265, 41.87696], [-87.62635, 41.87696], [-87.62603, 41.87697], [-87.62605, 41.87831], [-87.6261, 41.87951], [-87.62616, 41.88203], [-87.62619, 41.88322], [-87.62622, 41.88443], [-87.62626, 41.88534], [-87.62625, 41.88552], [-87.62625, 41.88557], [-87.62627, 41.88562], [-87.6263, 41.88566], [-87.62635, 41.88569], [-87.62642, 41.88572], [-87.6265, 41.88573], [-87.62655, 41.88574], [-87.62661, 41.88574], [-87.62683, 41.88574], [-87.62784, 41.88574], [-87.62887, 41.88574], [-87.62948, 41.88574], [-87.62982, 41.88574], [-87.62992, 41.88574], [-87.63011, 41.88574], [-87.6302, 41.88574], [-87.63089, 41.88574], [-87.63204, 41.88574], [-87.63285, 41.88573], [-87.63391, 41.88572], [-87.63396, 41.88707], [-87.63397, 41.88897], [-87.63399, 41.88943], [-87.63402, 41.88952], [-87.63407, 41.88962], [-87.63414, 41.8897], [-87.63425, 41.88979], [-87.63436, 41.88984], [-87.63447, 41.88989], [-87.63462, 41.88991], [-87.63475, 41.88994], [-87.63493, 41.88997], [-87.63508, 41.89001], [-87.63524, 41.89007], [-87.63533, 41.89012], [-87.6354, 41.89021], [-87.63548, 41.8903], [-87.63553, 41.89051], [-87.63554, 41.89147], [-87.63558, 41.89261], [-87.6356, 41.89327], [-87.63563, 41.89434], [-87.63565, 41.89542], [-87.63568, 41.89633], [-87.63569, 41.89645], [-87.63574, 41.89654], [-87.63583, 41.89667], [-87.63595, 41.89677], [-87.63623, 41.89699], [-87.63634, 41.89708], [-87.63641, 41.89715], [-87.63649, 41.89726], [-87.63654, 41.89738], [-87.63654, 41.89761], [-87.63657, 41.89867], [-87.63658, 41.89892], [-87.63662, 41.89975], [-87.63664, 41.90008], [-87.63664, 41.90029], [-87.6367, 41.90119], [-87.6367, 41.90131], [-87.63669, 41.90143], [-87.63666, 41.90156], [-87.63659, 41.90177], [-87.63653, 41.9019], [-87.6365, 41.90202], [-87.63647, 41.90211], [-87.63647, 41.90221], [-87.63646, 41.90235], [-87.63649, 41.90245], [-87.63653, 41.90255], [-87.63659, 41.90265], [-87.63671, 41.90279], [-87.63682, 41.90292], [-87.63688, 41.90299], [-87.63694, 41.90312], [-87.63699, 41.90322], [-87.63701, 41.90334], [-87.63702, 41.90368], [-87.63702, 41.90381], [-87.63711, 41.90543], [-87.63713, 41.90607], [-87.63718, 41.90758], [-87.63723, 41.90874], [-87.63725, 41.90951], [-87.63726, 41.90973], [-87.63727, 41.90986], [-87.6373, 41.90999], [-87.63736, 41.91011], [-87.63746, 41.91021], [-87.63757, 41.91027], [-87.63771, 41.91034], [-87.63787, 41.91039], [-87.63798, 41.91041], [-87.63814, 41.91042], [-87.6393, 41.91041], [-87.63975, 41.9104], [-87.64038, 41.9104], [-87.641, 41.9104], [-87.64255, 41.91039], [-87.64348, 41.91036], [-87.64523, 41.91034], [-87.6458, 41.91034], [-87.64646, 41.91033], [-87.64664, 41.91033], [-87.64682, 41.91034], [-87.64697, 41.91036], [-87.64709, 41.9104], [-87.64718, 41.91044], [-87.64728, 41.91051], [-87.64734, 41.91061], [-87.64736, 41.91071], [-87.64738, 41.91086], [-87.64739, 41.91101], [-87.6474, 41.91112], [-87.64745, 41.91122], [-87.64756, 41.91139], [-87.64768, 41.91146], [-87.64781, 41.9115], [-87.64792, 41.91151], [-87.64863, 41.9115], [-87.64883, 41.9115], [-87.649, 41.9115], [-87.6492, 41.91153], [-87.64939, 41.91158], [-87.64948, 41.9116], [-87.64957, 41.91164], [-87.64971, 41.91171], [-87.64985, 41.9118], [-87.65017, 41.91203], [-87.65093, 41.9126], [-87.65183, 41.91322], [-87.65208, 41.91339], [-87.65223, 41.91351], [-87.65234, 41.91362], [-87.65242, 41.91377], [-87.6525, 41.91406], [-87.65255, 41.91563], [-87.65261, 41.91718], [-87.65262, 41.91763], [-87.65264, 41.91808], [-87.65264, 41.91822], [-87.65274, 41.92113], [-87.65276, 41.92172], [-87.65282, 41.92352], [-87.65287, 41.92505], [-87.65288, 41.92537], [-87.65289, 41.92599], [-87.65292, 41.92715], [-87.65294, 41.92768], [-87.65295, 41.92842], [-87.65296, 41.9287], [-87.65299, 41.92951], [-87.65302, 41.9303], [-87.65305, 41.9309], [-87.65307, 41.93151], [-87.6531, 41.93218], [-87.65313, 41.93273], [-87.65314, 41.93312], [-87.6532, 41.93445], [-87.65322, 41.93491], [-87.65323, 41.93532], [-87.65327, 41.93603], [-87.65328, 41.93627], [-87.65331, 41.93717], [-87.65333, 41.93763], [-87.65336, 41.93865], [-87.65337, 41.93893], [-87.65337, 41.93939], [-87.65338, 41.93975], [-87.65345, 41.94218], [-87.65347, 41.94238], [-87.65351, 41.94257], [-87.65355, 41.94269], [-87.65361, 41.9428], [-87.65367, 41.94292], [-87.65375, 41.94306], [-87.65382, 41.94319], [-87.65392, 41.94331], [-87.654, 41.94342], [-87.6541, 41.94351], [-87.65425, 41.94364], [-87.65441, 41.94375], [-87.65454, 41.9438], [-87.6547, 41.94386], [-87.65488, 41.9439], [-87.65503, 41.94393], [-87.65521, 41.94394], [-87.6555, 41.94394], [-87.65608, 41.94393], [-87.65767, 41.94391], [-87.6591, 41.94391], [-87.66361, 41.94381], [-87.66508, 41.94379], [-87.66585, 41.94378], [-87.66749, 41.94377], [-87.66938, 41.94373], [-87.67088, 41.94371], [-87.6715, 41.9437], [-87.67234, 41.94369], [-87.67263, 41.94369], [-87.67289, 41.9437], [-87.67311, 41.94373], [-87.67334, 41.94376], [-87.67348, 41.9438], [-87.67367, 41.94386], [-87.67386, 41.94393], [-87.67405, 41.94404], [-87.67419, 41.94414], [-87.6743, 41.94424], [-87.67442, 41.94438], [-87.6745, 41.94455], [-87.67457, 41.94474], [-87.67463, 41.94494], [-87.67466, 41.94513], [-87.67468, 41.94596], [-87.67474, 41.94703], [-87.67476, 41.94859], [-87.67479, 41.94959], [-87.67484, 41.95101], [-87.67485, 41.95202], [-87.67487, 41.95302], [-87.67491, 41.95392], [-87.67496, 41.95452], [-87.67497, 41.95513], [-87.67501, 41.95666], [-87.67505, 41.95798], [-87.67513, 41.96077], [-87.67517, 41.96175], [-87.67519, 41.96371], [-87.6752, 41.96434], [-87.67521, 41.96457], [-87.67522, 41.96477], [-87.67523, 41.96491], [-87.67524, 41.96503], [-87.6753, 41.96519], [-87.67539, 41.96536], [-87.67544, 41.96547], [-87.67557, 41.96565], [-87.6757, 41.96579], [-87.67589, 41.96596], [-87.67613, 41.96612], [-87.67638, 41.96623], [-87.67665, 41.96632], [-87.67695, 41.96638], [-87.67722, 41.9664], [-87.67741, 41.96641], [-87.67766, 41.96642], [-87.67863, 41.96641], [-87.68034, 41.96638], [-87.68159, 41.96636], [-87.68271, 41.96635], [-87.68399, 41.96632], [-87.68545, 41.96631], [-87.68718, 41.96628], [-87.6885, 41.96625], [-87.68946, 41.96625], [-87.69026, 41.96624], [-87.69145, 41.96623], [-87.69411, 41.96621], [-87.69502, 41.96621], [-87.69675, 41.9662], [-87.69789, 41.96618], [-87.69873, 41.96616], [-87.69954, 41.96615], [-87.70075, 41.96615], [-87.70166, 41.96615], [-87.70267, 41.96614], [-87.70399, 41.96613], [-87.70645, 41.96612], [-87.70763, 41.96611], [-87.70884, 41.96609], [-87.71027, 41.96607], [-87.71108, 41.96607], [-87.71173, 41.96606], [-87.71219, 41.96605], [-87.7124, 41.96605], [-87.71255, 41.96605], [-87.71268, 41.96605], [-87.7128, 41.96607], [-87.71292, 41.96611], [-87.71298, 41.96615], [-87.71303, 41.96621], [-87.71309, 41.9663], [-87.7131, 41.96635], [-87.71311, 41.96643], [-87.71312, 41.96667], [-87.71314, 41.96793]]}'  # noqa: E501
