﻿{
  "appId": "com.aivision.app",
  "productName": "AI Vision App",
  "directories": {
    "output": "release"
  },
  "files": [
    "dist/**/*",
    "electron/**/*",
    "package.json"
  ],
  "extraResources": [
    {
      "from": "../Backend_Django",
      "to": "Backend_Django",
      "filter": [
        "**/*",
        "!**/__pycache__/**",
        "!**/*.pyc",
        "!**/.git/**",
        "!**/node_modules/**",
        "!**/.pytest_cache/**",
        "!**/test_*",
        "!**/*_test.py",
        "!**/venv/**"
      ]
    },
    {
      "from": "../Backend_Django/python_portable",
      "to": "python_portable",
      "filter": [
        "**/*"
      ]
    }
  ],
  "compression": "normal",
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": ["x64"]
      }
    ],
    "requestedExecutionLevel": "asInvoker"
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "shortcutName": "AI Vision App",
    "allowElevation": false,
    "perMachine": false,
    "deleteAppDataOnUninstall": false
  },
  "mac": {
    "target": ["dmg"]
  },
  "linux": {
    "target": ["AppImage"],
    "category": "Development"
  }
}
