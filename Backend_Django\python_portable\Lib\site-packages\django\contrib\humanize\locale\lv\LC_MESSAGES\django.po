# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2017
# <AUTHOR> <EMAIL>, 2017-2018
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2021-10-06 05:15+0000\n"
"Last-Translator: NullIsNot0 <<EMAIL>>\n"
"Language-Team: Latvian (http://www.transifex.com/django/django/language/"
"lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : "
"2);\n"

msgid "Humanize"
msgstr "Padarīt cilvēcīgu"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s miljonu"
msgstr[1] "%(value)s miljons"
msgstr[2] "%(value)s miljonu"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s miljardu"
msgstr[1] "%(value)s miljards"
msgstr[2] "%(value)s miljardu"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s triljonu"
msgstr[1] "%(value)s triljons"
msgstr[2] "%(value)s triljonu"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s kvadriljonu"
msgstr[1] "%(value)s kvadriljons"
msgstr[2] "%(value)s kvadriljonu"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s kvintiljonu"
msgstr[1] "%(value)s kvintiljons"
msgstr[2] "%(value)s kvintiljonu"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s sekstiljonu"
msgstr[1] "%(value)s sekstiljons"
msgstr[2] "%(value)s sekstiljonu"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septiljonu"
msgstr[1] "%(value)s septiljons"
msgstr[2] "%(value)s septiljonu"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s oktiljonu"
msgstr[1] "%(value)s oktiljons"
msgstr[2] "%(value)s oktiljonu"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s noniljonu"
msgstr[1] "%(value)s noniljons"
msgstr[2] "%(value)s noniljonu"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s dekaljonu"
msgstr[1] "%(value)s dekaljons"
msgstr[2] "%(value)s dekaljonu"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s gugolu"
msgstr[1] "%(value)s gugols"
msgstr[2] "%(value)s gugolu"

msgid "one"
msgstr "viens"

msgid "two"
msgstr "divi"

msgid "three"
msgstr "trīs"

msgid "four"
msgstr "četri"

msgid "five"
msgstr "pieci"

msgid "six"
msgstr "seši"

msgid "seven"
msgstr "septiņi"

msgid "eight"
msgstr "astoņi"

msgid "nine"
msgstr "deviņi"

msgid "today"
msgstr "šodien"

msgid "tomorrow"
msgstr "rīt"

msgid "yesterday"
msgstr "vakar"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "pirms %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "pirms %(count)s stundas"
msgstr[1] "pirms %(count)s stundām"
msgstr[2] "pirms %(count)s stundām"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "pirms %(count)s minūtes"
msgstr[1] "pirms %(count)s minūtēm"
msgstr[2] "pirms %(count)s minūtēm"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "pirms %(count)s sekundes"
msgstr[1] "pirms %(count)s sekundēm"
msgstr[2] "pirms %(count)s sekundēm"

msgid "now"
msgstr "tagad"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "pēc %(count)s sekundes"
msgstr[1] "pēc %(count)s sekundēm"
msgstr[2] "pēc %(count)s sekundēm"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "pēc %(count)s minūtes"
msgstr[1] "pēc %(count)s minūtēm"
msgstr[2] "pēc %(count)s minūtēm"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "pēc %(count)s stundas"
msgstr[1] "pēc %(count)s stundām"
msgstr[2] "pēc %(count)s stundām"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "pēc %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d gadi"
msgstr[1] "%(num)d gads"
msgstr[2] "%(num)d gadi"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mēneši"
msgstr[1] "%(num)d mēnesis"
msgstr[2] "%(num)d mēneši"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d nedēļas"
msgstr[1] "%(num)d nedēļa"
msgstr[2] "%(num)d nedēļas"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dienas"
msgstr[1] "%(num)d diena"
msgstr[2] "%(num)d dienas"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d stundas"
msgstr[1] "%(num)d stunda"
msgstr[2] "%(num)d stundas"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minūtes"
msgstr[1] "%(num)d minūte"
msgstr[2] "%(num)d minūtes"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d gadi"
msgstr[1] "%(num)d gads"
msgstr[2] "%(num)d gadi"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mēneši"
msgstr[1] "%(num)d mēnesis"
msgstr[2] "%(num)d mēneši"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d nedēļas"
msgstr[1] "%(num)d nedēļa"
msgstr[2] "%(num)d nedēļas"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d dienas"
msgstr[1] "%(num)d diena"
msgstr[2] "%(num)d dienas"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d stundas"
msgstr[1] "%(num)d stunda"
msgstr[2] "%(num)d stundas"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d minūtes"
msgstr[1] "%(num)d minūte"
msgstr[2] "%(num)d minūtes"
