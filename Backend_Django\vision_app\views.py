from django.http import JsonResponse
from ultralytics import YOLO
import json
import os
import logging
import tempfile
from django.conf import settings
from django.core.files.storage import default_storage # 新增：文件存储
from django.utils import timezone # 新增：处理时间
import uuid # 新增：生成唯一ID
import mimetypes
from urllib.parse import quote, unquote
from functools import wraps
from django.db import models

from rest_framework.decorators import api_view
from rest_framework.views import APIView # 新增：类基础视图
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser, FormParser # 新增：解析器
from rest_framework import status

from .models import AIModel, ExampleImage
from .serializers import AIModelSerializer, AIModelUploadSerializer, AIModelUpdateSerializer # 修改：导入新的序列化器
from collections import defaultdict # Added import
from .ocr_paddle_predictor import PaddleOCRSystemPredictor # OCR Predictor
from .ai_restored_onnxruntime_predictor import AIRestoredOnnxRuntimePredictor # AI Restore Predictor
from .feature_matching_traditional_predictor import TraditionalFeatureMatcher # 特征点匹配
import cv2 # 导入OpenCV
import numpy as np # 导入Numpy
from .feature_matching_onnxruntime_model_predictor import ModelBasedFeatureMatcher # 模型特征点匹配

 # 获取一个logger实例
logger = logging.getLogger(__name__)

# --- 管理员权限验证装饰器 ---

def require_admin_auth(view_func):
    """
    管理员权限验证装饰器
    检查用户是否具有管理员权限
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # 检查会话中是否有管理员标识
        if not request.session.get('is_admin', False):
            logger.warning(f"Unauthorized admin access attempt to {view_func.__name__}")
            return Response({
                'success': False,
                'message': '需要管理员权限才能访问此功能'
            }, status=status.HTTP_403_FORBIDDEN)

        # 如果有管理员权限，继续执行原视图函数
        return view_func(request, *args, **kwargs)

    return wrapper

@api_view(['GET'])
def list_models(request): # 重命名函数
    """
    获取AI模型列表，并按模型类型 (model_type) 分组。
    可以通过以下查询参数来筛选模型：
    - 'model_scope':
        - 'system': (默认) 只返回系统模型 (is_system_model=True)。
        - 'custom': 只返回自定义模型 (is_system_model=False)。
        - 'all': 返回所有模型。
    - 'model_type_filter': 按指定的模型类型进行过滤 (例如, 'ocr', 'ai_restored').
    """
    try:
        model_scope = request.query_params.get('model_scope', 'system').lower()
        model_type_filter = request.query_params.get('model_type_filter', None)

        log_message_parts = [f"scope: '{model_scope}'"]
        if model_type_filter:
            log_message_parts.append(f"model_type_filter: '{model_type_filter}'")

        queryset = AIModel.objects.all()

        if model_scope == 'system':
            queryset = queryset.filter(is_system_model=True).order_by('name')
            logger.info(f"Initial filter: system models, ordered by name.")
        elif model_scope == 'custom':
            queryset = queryset.filter(is_system_model=False).order_by('-uploaded_at')
            logger.info(f"Initial filter: custom models, ordered by uploaded_at descending.")
        elif model_scope == 'all':
            queryset = queryset.order_by('-is_system_model', 'name')
            logger.info(f"Initial filter: all models, ordered by is_system_model then name.")
        else:
            logger.warning(f"Invalid model_scope '{model_scope}' provided. Defaulting to 'system'.")
            queryset = queryset.filter(is_system_model=True).order_by('name')
            # Update model_scope for logging consistency if it was invalid
            model_scope = 'system'
            log_message_parts[0] = f"scope: '{model_scope}' (defaulted)"


        if model_type_filter:
            queryset = queryset.filter(model_type=model_type_filter)
            logger.info(f"Applied model_type_filter: '{model_type_filter}'.")

        serializer = AIModelSerializer(queryset, many=True)

        grouped_models = defaultdict(list)
        for model_data in serializer.data:
            model_type_key = model_data.get('model_type') if model_data.get('model_type') is not None else 'unknown'
            grouped_models[model_type_key].append(model_data)

        logger.info(f"Successfully retrieved and grouped {len(queryset)} models for query ({', '.join(log_message_parts)}).")
        return Response(grouped_models, status=status.HTTP_200_OK)
    except Exception as e:
        current_scope_for_error = model_scope if 'model_scope' in locals() else "unknown"
        current_filter_for_error = model_type_filter if 'model_type_filter' in locals() and model_type_filter else "none"
        logger.error(f"Error retrieving models (scope: {current_scope_for_error}, filter: {current_filter_for_error}): {str(e)}", exc_info=True)
        return Response({'error': f'获取模型列表失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def detect_barcode_ultralytics(request):
    logger.info(f"detect_barcode_ultralytics POST request received.")
    image_file = request.FILES.get('image')
    if not image_file:
        logger.warning("No image file provided in the request.")
        return Response({'error': '必须提供图像文件 (image)。'}, status=status.HTTP_400_BAD_REQUEST)

    model_name_param = request.POST.get('model_name')
    model_file_param = request.FILES.get('model_file')

    model_path_to_load = None
    temp_model_path = None # 用于临时上传的模型文件

    try:
        if model_name_param and model_file_param:
            logger.warning("Both model_name and model_file provided. Please provide only one.")
            return Response({'error': '请只提供 model_name 或 model_file，不能同时提供。'}, status=status.HTTP_400_BAD_REQUEST)

        if model_name_param:
            logger.info(f"Attempting to use system model: {model_name_param}")
            try:
                # 尝试通过名称和类型查找模型，优先查找系统模型
                try:
                    ai_model_instance = AIModel.objects.get(name=model_name_param, is_system_model=True)
                    logger.info(f"Found system model matching name: {model_name_param}")
                except AIModel.DoesNotExist:
                    # 如果找不到系统模型，再尝试找自定义模型
                    try:
                        ai_model_instance = AIModel.objects.get(name=model_name_param, is_system_model=False)
                        logger.info(f"Found custom model matching name: {model_name_param}")
                    except AIModel.DoesNotExist:
                        logger.warning(f"Model with name '{model_name_param}' not found in database (checked both system and custom).")
                        return Response({'error': f'名为 {model_name_param} 的模型未找到。'}, status=status.HTTP_404_NOT_FOUND)

                # --- 正确构造模型文件路径 (使用新的 settings 和 model 字段) ---
                if ai_model_instance.is_system_model:
                    # 系统模型路径: settings.SYSTEM_MODELS_ROOT / model_type / model_file_name
                    # 假设 model_file 字段存储了文件名 (e.g., 'best.pt')
                    if not ai_model_instance.model_file or not ai_model_instance.model_file.name:
                         logger.error(f"System model '{ai_model_instance.name}' record is missing the model filename in the 'model_file' field.")
                         return Response({'error': f'系统模型 {ai_model_instance.name} 记录缺少模型文件名。'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    model_filename = os.path.basename(ai_model_instance.model_file.name) # 获取纯文件名
                    model_path_to_load = os.path.join(settings.SYSTEM_MODELS_ROOT, ai_model_instance.model_type, model_filename)
                    path_type = "System"
                    error_message_detail = f'系统模型文件 {model_filename} (类型: {ai_model_instance.model_type}) 未在预期路径 {model_path_to_load} 找到。'
                else:
                    # 自定义模型路径: 根据模型类别构建正确的路径
                    if not ai_model_instance.model_file:
                        logger.error(f"Custom model '{ai_model_instance.name}' record is missing the model file reference.")
                        return Response({'error': f'自定义模型 {ai_model_instance.name} 记录缺少模型文件引用。'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    # 根据模型类别选择正确的基础路径
                    if ai_model_instance.is_system_model:
                        # 系统模型存储在 SYSTEM_MODELS_ROOT
                        base_path = settings.SYSTEM_MODELS_ROOT
                        path_type = "System"
                    else:
                        # 用户模型存储在 MEDIA_ROOT
                        base_path = settings.MEDIA_ROOT
                        path_type = "Custom"

                    # 构建完整路径
                    model_path_to_load = os.path.join(base_path, ai_model_instance.model_file.name)
                    error_message_detail = f'{path_type}模型文件 {ai_model_instance.model_file.name} 未在预期路径 {model_path_to_load} 找到。'

                logger.info(f"Attempting to load {path_type} model '{ai_model_instance.name}'. Path: {model_path_to_load}")

                # 检查文件是否存在 (对于 FileField， .path 已经是绝对路径)
                if not os.path.exists(model_path_to_load):
                    logger.error(f"{path_type} model file not found at path: {model_path_to_load} for model name: {ai_model_instance.name}")
                    return Response({'error': error_message_detail}, status=status.HTTP_404_NOT_FOUND)

                logger.info(f"{path_type} model file path confirmed: {model_path_to_load}")
                # --- 路径构造结束 ---

            except AIModel.MultipleObjectsReturned:
                 logger.error(f"Multiple models found with name '{model_name_param}'. This should not happen due to unique constraints.")
                 return Response({'error': f'发现多个名为 {model_name_param} 的模型记录，请联系管理员。'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            except Exception as e:
                logger.warning(f"System model with name '{model_name_param}' not found in database.")
                return Response({'error': f'名为 {model_name_param} 的系统模型未找到。'}, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                logger.error(f"Error accessing system model '{model_name_param}': {str(e)}", exc_info=True)
                return Response({'error': f'处理系统模型时出错: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        elif model_file_param:
            logger.info(f"Attempting to use uploaded model file: {model_file_param.name}")
            try:
                # 获取文件后缀，默认为 .pt
                original_suffix = os.path.splitext(model_file_param.name)[1]
                suffix = original_suffix if original_suffix else '.pt'

                with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
                    for chunk in model_file_param.chunks():
                        tmp_file.write(chunk)
                    temp_model_path = tmp_file.name
                model_path_to_load = temp_model_path
                logger.info(f"Uploaded model file saved to temporary path: {temp_model_path}")
            except Exception as e:
                logger.error(f"Error saving uploaded model file to temporary location: {str(e)}", exc_info=True)
                if temp_model_path and os.path.exists(temp_model_path):
                    os.remove(temp_model_path) # 清理以防万一
                return Response({'error': f'保存上传的模型文件失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        else:
            logger.warning("Neither model_name nor model_file was provided.")
            return Response({'error': '必须提供 model_name (使用系统模型) 或 model_file (上传临时模型)。'}, status=status.HTTP_400_BAD_REQUEST)

        # 加载模型
        try:
            logger.info(f"Attempting to load YOLO model from: {model_path_to_load}")
            model = YOLO(model_path_to_load)
            logger.info("YOLO model loaded successfully.")
        except Exception as e:
            logger.error(f"Failed to load YOLO model from {model_path_to_load}: {str(e)}", exc_info=True)
            return Response({'error': f'加载模型失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 处理图像和推理
        temp_image_path = None
        results_data = None
        try:
            # 保存上传的图像到临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(image_file.name)[1] or '.jpg') as tmp_img:
                for chunk in image_file.chunks():
                    tmp_img.write(chunk)
                temp_image_path = tmp_img.name
            logger.info(f"Uploaded image saved to temporary path: {temp_image_path}")

            conf_threshold = float(request.POST.get('confidence_threshold', 0.25))
            device_setting = request.POST.get('device', '') # '' for auto

            # 获取预处理方法参数
            preprocessing_method = request.POST.get('preprocessing_method', 'full_scale')
            logger.info(f"Received preprocessing_method: {preprocessing_method}")

            if preprocessing_method == "full_scale":
                logger.info("Processing with 'full_scale': Expecting a full image, ultralytics will handle scaling and padding.")
            elif preprocessing_method == "roi_area":
                logger.info("Processing with 'roi_area': Expecting a pre-cropped ROI image from the frontend.")
            else:
                # 如果提供了未知的 preprocessing_method，可以记录一个警告并使用默认值
                logger.warning(f"Unknown preprocessing_method: '{preprocessing_method}'. Defaulting to 'full_scale'.")
                # preprocessing_method = "full_scale" # 如果需要强制默认值
                logger.info("Processing with 'full_scale' (defaulted): Expecting a full image, ultralytics will handle scaling and padding.")

            # 可以在此添加对 preprocessing_method 值的进一步验证，如果需要
            # 例如:
            # allowed_methods = ["full_scale", "roi_area"]
            # if preprocessing_method not in allowed_methods:
            #     logger.warning(f"Invalid preprocessing_method: {preprocessing_method}. Defaulting to 'full_scale'.")
            #     preprocessing_method = 'full_scale'

            logger.info(f"Performing inference on {temp_image_path} with conf: {conf_threshold}, device: '{device_setting}', effective preprocessing: {preprocessing_method}")
            results = model.predict(source=temp_image_path, conf=conf_threshold, device=device_setting)
            logger.info("Inference completed.")

            detections = []
            if results and len(results) > 0:
                for result in results:
                    if result.boxes:
                        for box in result.boxes:
                            try:
                                detections.append({
                                    'box': box.xyxy[0].tolist(),
                                    'confidence': box.conf[0].item(),
                                    'class_id': int(box.cls[0].item()),
                                    'class_name': model.names[int(box.cls[0].item())]
                                })
                            except Exception as box_e:
                                logger.error(f"Error processing a detection box: {str(box_e)}", exc_info=True)
            results_data = {'detections': detections}
            logger.info(f"Successfully processed request. Returning {len(detections)} detections.")
            return Response(results_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error during image processing or inference: {str(e)}", exc_info=True)
            return Response({'error': f'图像处理或推理失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        finally:
            if temp_image_path and os.path.exists(temp_image_path):
                os.remove(temp_image_path)
                logger.debug(f"Temporary image {temp_image_path} removed.")

    except Exception as e:
        logger.error(f"An unexpected error occurred in detect_barcode_ultralytics: {str(e)}", exc_info=True)
        return Response({'error': f'发生意外错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    finally:
        # 清理临时上传的模型文件
        if temp_model_path and os.path.exists(temp_model_path):
            try:
                os.remove(temp_model_path)
                logger.info(f"Temporary model file {temp_model_path} removed.")
            except Exception as e:
                logger.error(f"Error removing temporary model file {temp_model_path}: {str(e)}", exc_info=True)

class AIModelUploadView(APIView):
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        logger.info(f"AIModelUploadView POST request received.")
        upload_serializer = AIModelUploadSerializer(data=request.data)
        if upload_serializer.is_valid():
            validated_data = upload_serializer.validated_data

            # model_file 是一个 UploadedFile 对象
            # name, model_type, version, description 来自序列化器

            try:
                # 在创建模型之前检查是否存在重复
                model_name = validated_data['name']
                model_type = validated_data['model_type']
                ocr_role = validated_data.get('ocr_role')
                is_system_model = validated_data.get('is_system_model', False)  # 获取模型类别

                # 构建查询条件
                filter_kwargs = {
                    'name': model_name,
                    'model_type': model_type,
                    'is_system_model': is_system_model  # 使用用户选择的模型类别
                }

                if model_type == 'ocr':
                    filter_kwargs['ocr_role'] = ocr_role

                existing_model = AIModel.objects.filter(**filter_kwargs).first()

                if existing_model:
                    # 如果存在同名模型，返回详细的错误信息
                    if model_type == 'ocr':
                        error_msg = f"已存在名为 '{model_name}' 的{model_type}模型（角色: {ocr_role}）"
                    else:
                        error_msg = f"已存在名为 '{model_name}' 的{model_type}模型"

                    # 如果有版本信息，建议使用不同的版本号
                    if validated_data.get('version'):
                        error_msg += f"。建议使用不同的版本号或模型名称。"
                    else:
                        error_msg += f"。建议添加版本号或使用不同的模型名称。"

                    logger.warning(f"Duplicate model upload attempt: {error_msg}")
                    return Response({
                        'success': False,
                        'message': error_msg
                    }, status=status.HTTP_400_BAD_REQUEST)

                # 创建 AIModel 实例。
                # 当我们将 UploadedFile 对象赋给 FileField (model_file) 并调用 save() 时，
                # Django 会自动处理文件的保存。
                # 文件将根据 MEDIA_ROOT 和 AIModel.model_file.upload_to (即 get_model_upload_path) 保存。
                # get_model_upload_path 返回 <model_type>/<original_filename>
                # Django 的 FileSystemStorage 会在文件名冲突时自动处理（通常附加随机字符）。
                # 数据库中 model_file 字段将存储 <model_type>/<actual_saved_filename>

                ai_model_instance = AIModel(
                    name=model_name,
                    model_type=model_type,
                    version=validated_data['version'],  # 版本号现在是必填的
                    description=validated_data.get('description'),
                    ocr_role=ocr_role,  # 添加OCR角色字段
                    model_file=validated_data['model_file'], # 直接传递 UploadedFile 对象
                    is_system_model=is_system_model,  # 使用用户选择的模型类别
                    ocr_collection_name=validated_data.get('ocr_collection_name')  # 新增OCR集合名称
                    # uploaded_at 会由 model 的 save 方法或 default=timezone.now 处理
                )
                ai_model_instance.save() # 这将触发文件保存和路径生成

                logger.info(f"AIModel record created for '{ai_model_instance.name}' with ID: {ai_model_instance.id}. Model path in DB: {ai_model_instance.model_file.name}")

                response_serializer = AIModelSerializer(ai_model_instance)
                return Response({
                    'success': True,
                    'message': '模型上传成功',
                    'model': response_serializer.data
                }, status=status.HTTP_201_CREATED)

            except Exception as e:
                logger.error(f"Error during AIModel instance creation or saving for model '{validated_data['name']}': {str(e)}", exc_info=True)
                # 注意：如果 ai_model_instance.save() 失败，文件可能已保存也可能未保存，
                # 取决于失败发生在哪一步。Django FileField 的保存通常是原子性的，
                # 但如果 save() 方法中的其他逻辑（非文件操作）失败，文件可能已写入。
                # 复杂的清理逻辑可能需要检查文件是否已存在并尝试删除。
                # 为简单起见，这里不添加复杂的清理逻辑，但生产环境中应考虑。
                return Response({
                    'success': False,
                    'message': f'处理模型上传失败: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            logger.warning(f"AIModelUploadSerializer validation errors: {upload_serializer.errors}")
            return Response({
                'success': False,
                'message': '模型上传参数验证失败',
                'errors': upload_serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
def detect_ocr_paddle_view(request):
    logger.info(f"detect_ocr_paddle_view POST request received.")
    if request.method == 'POST':
        image_file = request.FILES.get('image')
        if not image_file:
            logger.warning("No image file provided for OCR.")
            return Response({'error': '必须提供图像文件 (image)。'}, status=status.HTTP_400_BAD_REQUEST)

        ocr_task_name = request.POST.get('ocr_task_name')
        if not ocr_task_name:
            logger.warning("No ocr_task_name provided.")
            return Response({'error': '必须提供 ocr_task_name 参数。'}, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"OCR task: '{ocr_task_name}'")

        # --- 1. 查询模型 ---
        try:
            # For now, assume system models for OCR. This can be expanded later.
            det_model_instance = AIModel.objects.get(
                name=ocr_task_name,
                model_type='ocr',
                ocr_role='detection',
                is_system_model=True
            )
            rec_model_instance = AIModel.objects.get(
                name=ocr_task_name,
                model_type='ocr',
                ocr_role='recognition',
                is_system_model=True
            )
            logger.info(f"Found OCR models for task '{ocr_task_name}': Det='{det_model_instance.model_file.name}', Rec='{rec_model_instance.model_file.name}'")
        except AIModel.DoesNotExist:
            logger.warning(f"OCR models for task '{ocr_task_name}' (detection or recognition role) not found in system models.")
            return Response({'error': f"任务 '{ocr_task_name}' 对应的系统OCR检测或识别模型未找到。"}, status=status.HTTP_404_NOT_FOUND)
        except AIModel.MultipleObjectsReturned:
            logger.error(f"Multiple OCR models found for task '{ocr_task_name}' with the same role. Check database consistency.")
            return Response({'error': f"任务 '{ocr_task_name}' 发现重复的系统模型记录，请联系管理员。"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            logger.error(f"Error querying OCR models for task '{ocr_task_name}': {str(e)}", exc_info=True)
            return Response({'error': f"查询OCR模型时出错: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # --- 2. 构建模型路径 (for system models) ---
        # model_file field stores relative path like 'ocr/car_liencese/AI_OCR_Det_CHNLP_NCHW_1x3x320x320'
        # Predictor expects path to 'inference' directory.
        try:
            if not det_model_instance.model_file or not det_model_instance.model_file.name:
                logger.error(f"System detection model for task '{ocr_task_name}' is missing 'model_file' path.")
                return Response({'error': '系统检测模型记录缺少文件路径信息。'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            if not rec_model_instance.model_file or not rec_model_instance.model_file.name:
                logger.error(f"System recognition model for task '{ocr_task_name}' is missing 'model_file' path.")
                return Response({'error': '系统识别模型记录缺少文件路径信息。'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Assuming SYSTEM_MODELS_ROOT is already a Path object or string that can be joined
            det_model_base_path = os.path.join(settings.SYSTEM_MODELS_ROOT, det_model_instance.model_file.name)
            rec_model_base_path = os.path.join(settings.SYSTEM_MODELS_ROOT, rec_model_instance.model_file.name)

            det_model_dir_full = os.path.join(det_model_base_path, 'inference')
            rec_model_dir_full = os.path.join(rec_model_base_path, 'inference')

            logger.info(f"Det model path for predictor: {det_model_dir_full}")
            logger.info(f"Rec model path for predictor: {rec_model_dir_full}")

            if not os.path.isdir(det_model_dir_full):
                logger.error(f"Detection model inference directory not found: {det_model_dir_full}")
                return Response({'error': f'检测模型推断目录不存在: {det_model_instance.model_file.name}/inference'}, status=status.HTTP_404_NOT_FOUND)
            if not os.path.isdir(rec_model_dir_full):
                logger.error(f"Recognition model inference directory not found: {rec_model_dir_full}")
                return Response({'error': f'识别模型推断目录不存在: {rec_model_instance.model_file.name}/inference'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Error constructing model paths for task '{ocr_task_name}': {str(e)}", exc_info=True)
            return Response({'error': f"构建模型路径时出错: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # --- 3. 获取预测器参数 ---
        # Get base parameters from settings.OCR_TASK_CONFIGS
        task_configs = settings.OCR_TASK_CONFIGS.get(ocr_task_name, settings.OCR_TASK_CONFIGS.get('default'))
        # Deepcopy to prevent modification of settings dictionary if allowing API overrides later
        predictor_params = task_configs.copy()

        # (Optional) Allow API to override certain parameters, e.g., use_gpu
        # Example: Override use_gpu if provided in the request
        if 'use_gpu' in request.POST:
            try:
                predictor_params['use_gpu'] = str(request.POST['use_gpu']).lower() in ('true', '1', 't')
                logger.info(f"Overriding 'use_gpu' to {predictor_params['use_gpu']} based on API request.")
            except Exception as e_gpu_override:
                logger.warning(f"Could not parse 'use_gpu' from request: {request.POST['use_gpu']}. Error: {e_gpu_override}. Using configured value.")

        # Ensure critical algorithm params are not easily overridden if they should be tied to the model task
        # For example, det_algorithm, rec_algorithm, lang, rec_image_shape for a specific task
        # If cls_model_dir is needed (e.g. general_document_ocr with use_angle_cls=True) and it comes from settings.OCR_TASK_CONFIGS,
        # ensure it's correctly passed or constructed if it contains dynamic parts like SYSTEM_MODELS_ROOT
        # Current example for general_document_ocr in settings.py has a placeholder for cls_model_dir.
        # If a task config (like general_document_ocr) has 'cls_model_dir' set (and use_angle_cls=True),
        # and if that path needs to be absolute (like det/rec model dirs), it should be handled here or in settings.
        # For now, assuming cls_model_dir if present in task_configs is already a usable path or predictor handles relative.
        # PaddleOCRSystemPredictor expects absolute path for cls_model_dir if use_angle_cls is True.
        # So if cls_model_dir is defined in OCR_TASK_CONFIGS as a relative path (e.g. 'ocr/general_document/cls_model/inference'),
        # it needs to be joined with SYSTEM_MODELS_ROOT here:
        if predictor_params.get('use_angle_cls') and predictor_params.get('cls_model_dir') and isinstance(predictor_params.get('cls_model_dir'), str) and not os.path.isabs(predictor_params.get('cls_model_dir')):
            relative_cls_path = predictor_params['cls_model_dir']
            # Check if it might be relative to SYSTEM_MODELS_ROOT, e.g. starts with 'ocr/' or similar pattern
            # This logic might need to be more robust depending on how cls_model_dir paths are defined in settings
            if settings.SYSTEM_MODELS_ROOT: # Ensure SYSTEM_MODELS_ROOT is defined
                 potential_abs_path = os.path.join(settings.SYSTEM_MODELS_ROOT, relative_cls_path)
                 if os.path.isdir(potential_abs_path):
                     predictor_params['cls_model_dir'] = potential_abs_path
                     logger.info(f"Converted relative cls_model_dir '{relative_cls_path}' to absolute: {predictor_params['cls_model_dir']}")
                 else:
                     logger.warning(f"Relative cls_model_dir '{relative_cls_path}' provided in config but corresponding absolute path not found: {potential_abs_path}. Predictor might fail if it requires an absolute path.")
            else:
                 logger.warning(f"Relative cls_model_dir '{relative_cls_path}' provided, but SYSTEM_MODELS_ROOT is not configured in settings. Predictor might fail.")

        logger.info(f"Using predictor_params for task '{ocr_task_name}': {predictor_params}")

        # --- 4. 实例化预测器 ---
        try:
            predictor = PaddleOCRSystemPredictor(
                det_model_dir=det_model_dir_full,
                rec_model_dir=rec_model_dir_full,
                **predictor_params
            )
            logger.info(f"PaddleOCRSystemPredictor initialized for task '{ocr_task_name}'.")
        except Exception as e:
            logger.error(f"Failed to initialize PaddleOCRSystemPredictor for task '{ocr_task_name}': {str(e)}", exc_info=True)
            return Response({'error': f'初始化OCR预测器失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # --- 5. 处理图像并预测 ---
        temp_image_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(image_file.name)[1] or '.jpg') as tmp_img:
                for chunk in image_file.chunks():
                    tmp_img.write(chunk)
                temp_image_path = tmp_img.name
            logger.info(f"Temporary image for OCR saved to: {temp_image_path}")

            # Predictor's predict method can take image path or cv2 array.
            # For simplicity, pass path. It handles reading.
            dt_boxes, rec_res, time_dict = predictor.predict(temp_image_path)
            logger.info(f"OCR prediction completed for task '{ocr_task_name}'.")

            # --- 6. 格式化结果 ---
            # rec_res format: list of tuples, e.g., [('text1', 0.99), ('text2', 0.95)]
            # dt_boxes format: list of numpy arrays, each [4, 2] points

            formatted_results = []
            if rec_res and dt_boxes and len(rec_res) == len(dt_boxes):
                for i in range(len(rec_res)):
                    text, confidence = rec_res[i]
                    box_points = dt_boxes[i].tolist() # Convert numpy array to list for JSON
                    formatted_results.append({
                        'text': text,
                        'confidence': float(confidence), # Ensure confidence is float
                        'box': box_points
                    })
            elif rec_res: # Handle case where only rec_res might be returned by some logic (though unlikely with current predictor)
                 for text, confidence in rec_res:
                      formatted_results.append({
                        'text': text,
                        'confidence': float(confidence),
                        'box': None # No corresponding box
                    })


            results_data = {
                'task_name': ocr_task_name,
                'results': formatted_results,
                'time_info': time_dict # time_dict is already a dict
            }
            logger.info(f"OCR task '{ocr_task_name}' successful. Returning {len(formatted_results)} results.")
            return Response(results_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error during OCR prediction processing for task '{ocr_task_name}': {str(e)}", exc_info=True)
            return Response({'error': f'OCR预测处理失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        finally:
            if temp_image_path and os.path.exists(temp_image_path):
                try:
                    os.remove(temp_image_path)
                    logger.debug(f"Temporary image {temp_image_path} removed for OCR task.")
                except Exception as e_remove:
                    logger.error(f"Error removing temporary image {temp_image_path}: {str(e_remove)}", exc_info=True)

    return Response({'error': 'Invalid request method.'}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

@api_view(['GET'])
def list_ocr_tasks(request):
    """
    获取可用的系统OCR模型列表。
    一个OCR任务被认为是可用的，如果它同时具有 'detection' 和 'recognition' 角色的系统模型。
    现在基于ocr_collection_name进行分组，而不是name字段。
    """
    try:
        logger.info("Attempting to retrieve available OCR tasks.")
        # 筛选所有系统OCR模型
        system_ocr_models = AIModel.objects.filter(model_type='ocr', is_system_model=True)

        # 按OCR集合名称分组，如果没有collection_name则使用name
        collections_with_roles = defaultdict(lambda: {'detection': None, 'recognition': None, 'task_name': None})

        for model in system_ocr_models:
            # 使用ocr_collection_name作为分组键，如果为空则使用name
            collection_key = model.ocr_collection_name or model.name

            if model.ocr_role == 'detection':
                collections_with_roles[collection_key]['detection'] = model
            elif model.ocr_role == 'recognition':
                collections_with_roles[collection_key]['recognition'] = model

            # 保存task_name（用于API调用）
            if not collections_with_roles[collection_key]['task_name']:
                collections_with_roles[collection_key]['task_name'] = model.name

        available_tasks = []
        for collection_name, roles_data in collections_with_roles.items():
            if roles_data['detection'] and roles_data['recognition']:
                available_tasks.append({
                    'task_name': roles_data['task_name'],  # 用于API调用的原始name
                    'display_name': collection_name  # 用于显示的集合名称
                })

        # 按显示名称排序以获得一致的顺序
        available_tasks.sort(key=lambda x: x['display_name'])

        logger.info(f"Successfully retrieved {len(available_tasks)} available OCR tasks.")
        return Response(available_tasks, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(f"Error retrieving OCR tasks: {str(e)}", exc_info=True)
        return Response({'error': f'获取OCR模型列表失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AIImageRestoreView(APIView):
    """
    AI图像修复的API视图。
    接受一个图像文件并返回修复后的图像。
    """
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        logger.info("AIImageRestoreView POST request received.")
        image_file = request.FILES.get('image')
        if not image_file:
            logger.warning("No image file provided for AI restoration.")
            return Response({'error': '必须提供图像文件 (image)。'}, status=status.HTTP_400_BAD_REQUEST)

        output_format = request.POST.get('output_format', 'PNG').upper()
        if output_format not in ['PNG', 'JPEG']:
            logger.warning(f"Invalid output_format '{output_format}' provided. Defaulting to 'PNG'.")
            output_format = 'PNG'

        # 获取模型选择参数
        model_name = request.POST.get('model_name', 'AI_Restorer_V1.0.1.7')  # 默认使用最新版本
        logger.info(f"Processing image restoration: model='{model_name}', output_format='{output_format}', using CPU for inference")

        try:
            # 根据模型名称获取模型文件路径
            try:
                ai_model = AIModel.objects.get(
                    name=model_name,
                    model_type='ai_restored',
                    is_system_model=True
                )

                # 获取模型文件的字符串路径
                if ai_model.model_file:
                    # 如果model_file字段有值，使用相对路径
                    model_file_relative_path = str(ai_model.model_file)  # 转换FieldFile为字符串
                    model_file_path = os.path.join(
                        settings.BASE_DIR,
                        'models', 'system_models', 'ai_restored',
                        model_file_relative_path
                    )
                else:
                    # 如果model_file字段为空，使用默认路径构建
                    logger.warning(f"AI model {ai_model.name} has no model_file specified, using default path")
                    model_file_path = os.path.join(
                        settings.BASE_DIR,
                        'models', 'system_models', 'ai_restored',
                        f"{ai_model.name}.onnx"
                    )

                # 检查文件是否存在
                if not os.path.exists(model_file_path):
                    logger.error(f"AI Restorer model file not found: {model_file_path}")
                    return Response({'error': f'AI修复模型文件未找到: {model_file_relative_path if ai_model.model_file else ai_model.name}'}, status=status.HTTP_400_BAD_REQUEST)

                logger.info(f"Using AI Restorer model: {ai_model.name} v{ai_model.version} (file: {model_file_relative_path if ai_model.model_file else 'default'})")
            except AIModel.DoesNotExist:
                logger.error(f"AI Restorer model '{model_name}' not found in database")
                return Response({'error': f'指定的AI修复模型 "{model_name}" 未找到'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                predictor = AIRestoredOnnxRuntimePredictor(model_path=model_file_path)
                logger.info(f"AIRestoredOnnxRuntimePredictor initialized with model: {model_file_path}")
            except Exception as e_init:
                logger.error(f"Failed to initialize AIRestoredOnnxRuntimePredictor: {e_init}", exc_info=True)
                return Response({'error': f'初始化AI图像修复器失败: {str(e_init)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            image_bytes = image_file.read()

            prediction_result = predictor.predict_restore_image(
                image_input=image_bytes,
                output_format=output_format
            )

            if prediction_result['status'] == 'success':
                logger.info("AI image restoration successful.")
                return Response(prediction_result, status=status.HTTP_200_OK)
            else:
                logger.error(f"AI image restoration failed: {prediction_result['message']}")
                return Response(prediction_result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"Unexpected error in AIImageRestoreView: {str(e)}", exc_info=True)
            return Response({'error': f'AI图像修复过程中发生意外错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def feature_matching_traditional_view(request):
    """
    传统特征点匹配API视图。
    接收模板图像、目标图像和模板ROI，返回匹配结果。
    """
    logger.info("feature_matching_traditional_view POST request received.")
    
    try:
        # 1. 获取和验证参数
        template_image_file = request.FILES.get('template_image')
        target_image_file = request.FILES.get('target_image')
        template_roi_str = request.POST.get('template_roi')

        if not all([template_image_file, target_image_file, template_roi_str]):
            return Response({'error': '必须提供 template_image, target_image 和 template_roi 参数。'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            template_roi = json.loads(template_roi_str)
            if not all(k in template_roi for k in ['x', 'y', 'width', 'height']):
                raise ValueError("ROI 必须包含 x, y, width, height。")
        except (json.JSONDecodeError, ValueError) as e:
            return Response({'error': f'无效的 template_roi 格式: {e}'}, status=status.HTTP_400_BAD_REQUEST)

        algorithm = request.POST.get('algorithm', 'SIFT')
        match_ratio_threshold = float(request.POST.get('match_ratio_threshold', 0.7))
        min_match_count = int(request.POST.get('min_match_count', 10))

        # 2. 将上传的文件读入OpenCV图像格式
        template_image_bytes = np.frombuffer(template_image_file.read(), np.uint8)
        target_image_bytes = np.frombuffer(target_image_file.read(), np.uint8)
        template_image = cv2.imdecode(template_image_bytes, cv2.IMREAD_COLOR)
        target_image = cv2.imdecode(target_image_bytes, cv2.IMREAD_COLOR)

        if template_image is None or template_image.size == 0:
            logger.error("Failed to decode template image.")
            return Response({'error': '无法解码模板图像文件，请确保文件格式正确。'}, status=status.HTTP_400_BAD_REQUEST)
        
        if target_image is None or target_image.size == 0:
            logger.error("Failed to decode target image.")
            return Response({'error': '无法解码目标图像文件，请确保文件格式正确。'}, status=status.HTTP_400_BAD_REQUEST)

        # 3. 实例化并执行匹配
        matcher = TraditionalFeatureMatcher(
            algorithm=algorithm,
            match_ratio_threshold=match_ratio_threshold,
            min_match_count=min_match_count
        )
        
        result = matcher.match(template_image, target_image, template_roi)

        # 4. 返回结果
        response_status = status.HTTP_200_OK if result['status'] == 'success' else status.HTTP_400_BAD_REQUEST
        return Response(result, status=response_status)

    except Exception as e:
        logger.error(f"特征点匹配视图发生意外错误: {str(e)}", exc_info=True)
        return Response({'error': f'处理请求时发生意外错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def list_example_images(request):
    """
    获取示例图片列表，数据源为 ExampleImage 模型。
    支持按分类和文件夹进行过滤。

    查询参数:
    - category: 'barcode', 'ocr', 'ai_restored' (必需)
    - folder: 文件夹路径，例如 'subfolder' 或 'invoices/2024' (可选)

    返回格式:
    {
        "images": [
            { "id": 1, "name": "image1.jpg", "url": "...", ... },
            ...
        ],
        "folders": [ "subfolder1", "subfolder2" ]
    }
    """
    try:
        source = request.query_params.get('source')

        # 如果是仪表盘请求，返回所有分类的图片
        if source == 'dashboard':
            logger.info("Retrieving all example images for dashboard statistics.")
            all_images = ExampleImage.objects.all().order_by('display_order')
            
            response_data = {
                'barcode': [],
                'ocr': [],
                'ai_restored': []
            }

            for img in all_images:
                image_data = {
                    'id': img.id,
                    'name': img.name,
                    'path': img.path,
                    'category': img.category,
                    'display_order': img.display_order,
                    'description': img.description,
                    'file_size': img.file_size,
                    'width': img.width,
                    'height': img.height,
                    'url': img.url
                }
                if img.category in response_data:
                    response_data[img.category].append(image_data)
            
            logger.info(f"Dashboard data prepared: {len(response_data['barcode'])} barcode, {len(response_data['ocr'])} ocr, {len(response_data['ai_restored'])} ai_restored images.")
            return Response(response_data, status=status.HTTP_200_OK)

        # 否则，执行现有的按分类和文件夹过滤的逻辑
        else:
            category = request.query_params.get('category')
            folder = request.query_params.get('folder', '').strip('/')

            if not category:
                return Response({'error': '必须提供 category 查询参数。'}, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"Retrieving example images for category='{category}', folder='{folder}'")

            # 基础查询集
            queryset = ExampleImage.objects.filter(category=category)

            # --- 过滤图片 ---
            # 如果在根目录
            if not folder:
                # 只选择路径中不含'/'的图片，即根目录下的图片
                image_queryset = queryset.filter(path__regex=r'^[^/]+$')
            else:
                # 文件夹下的图片，路径以 "folder/" 开头，且后面不应再有'/'
                prefix = f"{folder}/"
                image_queryset = queryset.filter(path__startswith=prefix, path__regex=rf'^{prefix}[^/]+$')

            # 序列化图片数据
            images_data = []
            for img in image_queryset:
                images_data.append({
                    'id': img.id,
                    'name': img.name,
                    'path': img.path,
                    'category': img.category,
                    'display_order': img.display_order,
                    'description': img.description,
                    'file_size': img.file_size,
                    'width': img.width,
                    'height': img.height,
                    'url': img.url # 使用模型中的 @property
                })

            # --- 获取子文件夹 (数据库和文件系统结合) ---
            db_subfolders = set()
            
            # 1. 从数据库中获取包含图片的文件夹
            if not folder:
                paths_with_subdirs = queryset.filter(path__contains='/')
                db_subfolders.update(p.path.split('/')[0] for p in paths_with_subdirs)
            else:
                prefix = f"{folder}/"
                paths_in_folder = queryset.filter(path__startswith=prefix)
                for p in paths_in_folder:
                    path_without_prefix = p.path[len(prefix):]
                    if '/' in path_without_prefix:
                        db_subfolders.add(path_without_prefix.split('/')[0])
            
            # 2. 从文件系统扫描所有物理文件夹（包括空文件夹）
            fs_subfolders = set()
            try:
                example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', os.path.join(settings.BASE_DIR, 'models', 'example_images'))
                current_scan_dir = os.path.join(example_images_root, category, folder) if folder else os.path.join(example_images_root, category)
                
                if os.path.isdir(current_scan_dir):
                    for item in os.listdir(current_scan_dir):
                        item_path = os.path.join(current_scan_dir, item)
                        if os.path.isdir(item_path):
                            fs_subfolders.add(item)
            except Exception as e:
                logger.warning(f"Could not scan filesystem for folders in '{current_scan_dir}': {e}")

            # 3. 合并并排序
            subfolders = sorted(list(db_subfolders.union(fs_subfolders)))

            response_data = {
                'images': images_data,
                'folders': subfolders
            }

            logger.info(f"Found {len(images_data)} images and {len(subfolders)} folders for the request.")
            return Response(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error retrieving example images from database: {str(e)}", exc_info=True)
        return Response({'error': f'从数据库获取示例图片列表失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def serve_example_image(request, category, filename):
    """
    提供示例图片文件服务。

    参数:
    - category: 图片分类 (barcode, ocr, ai_restored)
    - filename: 图片文件名

    返回图片文件内容，支持浏览器缓存。
    """
    try:
        # URL解码文件名，处理包含中文字符的文件名
        decoded_filename = unquote(filename)
        logger.info(f"Serving example image: {category}/{decoded_filename} (original: {filename})")

        # 验证分类
        allowed_categories = {'barcode', 'ocr', 'ai_restored'}
        if category not in allowed_categories:
            logger.warning(f"Invalid category requested: {category}")
            return Response({'error': '无效的图片分类'}, status=status.HTTP_400_BAD_REQUEST)

        # 定义示例图片存储根目录
        example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', None)
        if not example_images_root:
            example_images_root = os.path.join(settings.BASE_DIR, 'models', 'example_images')

        # 构建文件路径，使用解码后的文件名
        file_path = os.path.join(example_images_root, category, decoded_filename)

        # 安全检查：确保文件路径在允许的目录内
        normalized_root = os.path.normpath(example_images_root)
        normalized_path = os.path.normpath(file_path)
        if not normalized_path.startswith(normalized_root):
            logger.warning(f"Path traversal attempt detected: {decoded_filename}")
            return Response({'error': '无效的文件路径'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件是否存在
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            logger.warning(f"Example image not found: {file_path}")
            return Response({'error': '图片文件未找到'}, status=status.HTTP_404_NOT_FOUND)

        # 检查文件格式
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.webp', '.avif'}
        file_ext = os.path.splitext(decoded_filename)[1].lower()
        if file_ext not in supported_formats:
            logger.warning(f"Unsupported file format: {file_ext}")
            return Response({'error': '不支持的文件格式'}, status=status.HTTP_400_BAD_REQUEST)

        # 获取MIME类型
        content_type, _ = mimetypes.guess_type(file_path)
        if not content_type:
            content_type = 'application/octet-stream'

        # 读取文件内容
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()
        except Exception as read_error:
            logger.error(f"Error reading example image file {file_path}: {str(read_error)}")
            return Response({'error': '读取图片文件失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 创建HTTP响应
        from django.http import HttpResponse
        response = HttpResponse(file_content, content_type=content_type)

        # 设置文件名
        response['Content-Disposition'] = f'inline; filename="{decoded_filename}"'

        # 设置缓存头
        response['Cache-Control'] = 'public, max-age=3600'  # 缓存1小时

        # 设置文件大小
        response['Content-Length'] = len(file_content)

        logger.debug(f"Successfully served example image: {category}/{decoded_filename} ({len(file_content)} bytes)")
        return response

    except Exception as e:
        logger.error(f"Error serving example image {category}/{decoded_filename if 'decoded_filename' in locals() else filename}: {str(e)}", exc_info=True)
        return Response({'error': f'提供图片文件服务失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# --- 管理员身份验证API ---

@api_view(['POST'])
def admin_login(request):
    """
    管理员登录API
    使用简单的密码验证机制
    """
    try:
        password = request.data.get('password')
        if not password:
            logger.warning("Admin login attempt without password")
            return Response({
                'success': False,
                'message': '请输入密码'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 简单的密码验证 - 固定密码 "mindeo"
        if password == 'mindeo':
            # 在会话中设置管理员状态
            request.session['is_admin'] = True
            request.session['admin_login_time'] = timezone.now().isoformat()

            logger.info("Admin login successful")
            return Response({
                'success': True,
                'message': '管理员登录成功'
            }, status=status.HTTP_200_OK)
        else:
            logger.warning(f"Admin login failed with incorrect password")
            return Response({
                'success': False,
                'message': '密码错误'
            }, status=status.HTTP_401_UNAUTHORIZED)

    except Exception as e:
        logger.error(f"Error during admin login: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'登录过程中发生错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def admin_logout(request):
    """
    管理员登出API
    清除会话中的管理员状态
    """
    try:
        # 无论当前是否已登录，都清除会话状态
        was_admin = request.session.get('is_admin', False)
        request.session.pop('is_admin', None)
        request.session.pop('admin_login_time', None)

        if was_admin:
            logger.info("Admin logout successful")
            return Response({
                'success': True,
                'message': '管理员登出成功'
            }, status=status.HTTP_200_OK)
        else:
            logger.info("Admin logout called but user was not logged in")
            return Response({
                'success': True,
                'message': '已登出'
            }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error during admin logout: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'登出过程中发生错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def admin_check_status(request):
    """
    检查管理员登录状态API
    """
    try:
        is_admin = request.session.get('is_admin', False)
        login_time = request.session.get('admin_login_time')

        return Response({
            'success': True,
            'is_admin': is_admin,
            'login_time': login_time
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error checking admin status: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'检查状态时发生错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def upload_example_image(request):
    """
    管理员上传示例图片API
    参数: file, category, name(可选), description(可选)
    """
    try:
        image_file = request.FILES.get('file')
        category = request.POST.get('category')
        name = request.POST.get('name')
        description = request.POST.get('description')
        allowed_categories = {'barcode', 'ocr', 'ai_restored'}
        supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.webp', '.avif'}
        if not image_file or not category:
            return Response({'error': '必须提供图片文件和分类'}, status=400)
        if category not in allowed_categories:
            return Response({'error': '无效的图片分类'}, status=400)
        ext = os.path.splitext(image_file.name)[1].lower()
        if ext not in supported_formats:
            return Response({'error': '不支持的图片格式'}, status=400)
        # 目标目录
        example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', None) or os.path.join(settings.BASE_DIR, 'models', 'example_images')
        category_dir = os.path.join(example_images_root, category)
        os.makedirs(category_dir, exist_ok=True)
        # 生成安全文件名
        base_name = name if name else os.path.splitext(image_file.name)[0]
        safe_base = base_name.replace(' ', '_').replace('/', '_')
        filename = f"{safe_base}{ext}"
        file_path = os.path.join(category_dir, filename)
        # 避免重名
        counter = 1
        while os.path.exists(file_path):
            filename = f"{safe_base}_{counter}{ext}"
            file_path = os.path.join(category_dir, filename)
            counter += 1
        # 保存文件
        with open(file_path, 'wb+') as f:
            for chunk in image_file.chunks():
                f.write(chunk)
        # 获取文件信息
        from PIL import Image as PILImage
        from django.db.models import Max
        
        try:
            with PILImage.open(file_path) as img:
                width, height = img.size
        except Exception:
            width, height = 0, 0
        
        file_size = os.path.getsize(file_path)

        # 为新图片计算 display_order，使其排在最后
        # 新上传的图片总是在根目录，所以我们只筛选根目录的图片
        max_order_result = ExampleImage.objects.filter(
            category=category,
            path__regex=r'^[^/]+$' # 匹配不包含'/'的路径，即根目录文件
        ).aggregate(max_order=Max('display_order'))
        
        max_order = max_order_result.get('max_order')
        new_order = max_order + 1 if max_order is not None else 0

        # 根据分类设置默认描述
        default_descriptions = {
            'barcode': '条码检测示例图片',
            'ocr': 'OCR检测示例图片',
            'ai_restored': 'AI修复示例图片'
        }
        final_description = description or default_descriptions.get(category, f"{category} 示例图片")

        # 创建并保存ExampleImage实例
        new_image = ExampleImage.objects.create(
            name=filename,
            path=filename,  # 对于根目录文件, path与name相同
            category=category,
            description=final_description,
            display_order=new_order,
            file_size=file_size,
            width=width,
            height=height
        )

        # 构建返回给前端的数据
        response_data = {
            'id': new_image.id,
            'name': new_image.name,
            'description': new_image.description,
            'url': new_image.url, # 使用模型属性获取URL
            'category': new_image.category,
            'file_size': new_image.file_size,
            'dimensions': [new_image.width, new_image.height]
        }
        
        logger.info(f"Example image uploaded and saved to DB: {category}/{filename} with ID {new_image.id}")
        return Response(response_data, status=status.HTTP_201_CREATED)
    except Exception as e:
        logger.error(f"Error uploading example image: {str(e)}", exc_info=True)
        return Response({'error': f'上传图片失败: {str(e)}'}, status=500)

@api_view(['POST'])
@require_admin_auth
def delete_example_images(request):
    """
    管理员批量删除示例图片API
    参数: ids (一个包含图片ID的数组)
    """
    try:
        data = request.data
        image_ids = data.get('ids', [])

        if not isinstance(image_ids, list) or not image_ids:
            return Response({'success': False, 'message': '参数错误，需要提供一个包含图片ID的数组'}, status=400)

        # 查找所有待删除的图片
        images_to_delete = ExampleImage.objects.filter(id__in=image_ids)
        
        deleted_count = 0
        failed_ids = []
        
        # 记录下所有有效的ID，用于后续比较
        valid_ids_found = {img.id for img in images_to_delete}

        # 遍历查询到的图片并删除
        for image in images_to_delete:
            image_id = image.id
            try:
                # 调用模型的delete方法，这将触发物理文件删除
                image.delete()
                deleted_count += 1
                logger.info(f"Successfully deleted ExampleImage with ID: {image_id}")
            except Exception as e:
                logger.error(f"Failed to delete ExampleImage with ID {image_id}: {str(e)}", exc_info=True)
                failed_ids.append(image_id)
        
        # 找出那些传入了但未在数据库中找到的ID
        requested_ids = set(image_ids)
        not_found_ids = list(requested_ids - valid_ids_found)
        
        failed_count = len(failed_ids) + len(not_found_ids)

        # 构建响应信息
        message = f'请求删除 {len(image_ids)} 张图片。成功删除 {deleted_count} 张。'
        if failed_count > 0:
            message += f' 失败 {failed_count} 张。'
            if not_found_ids:
                message += f' (ID: {not_found_ids} 未找到)'


        return Response({
            'success': failed_count == 0,
            'message': message,
            'deleted_count': deleted_count,
            'failed_count': failed_count,
            'failed_ids': failed_ids,
            'not_found_ids': not_found_ids
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error processing delete_example_images request: {str(e)}", exc_info=True)
        return Response({'success': False, 'message': f'删除图片过程中发生意外错误: {str(e)}'}, status=500)


# --- 模型管理API ---

@api_view(['POST'])
@require_admin_auth
def delete_models(request):
    """
    管理员批量删除模型API
    参数: model_ids (数组)
    """
    try:
        data = request.data
        model_ids = data.get('model_ids', [])

        if not isinstance(model_ids, list) or not model_ids:
            return Response({'success': False, 'message': '请提供要删除的模型ID列表'}, status=400)

        deleted_models = []
        failed_models = []

        for model_id in model_ids:
            try:
                # 获取模型实例
                model = AIModel.objects.get(id=model_id)

                # 记录模型信息用于日志
                model_info = f"{model.name} (ID: {model.id}, Type: {model.model_type})"

                # 保存模型信息用于响应
                model_data = {
                    'id': model_id,
                    'name': model.name,
                    'model_type': model.model_type
                }

                # 删除模型（包括文件和数据库记录）
                # 模型的delete方法会自动处理文件删除
                model.delete()

                deleted_models.append(model_data)
                logger.info(f"Successfully deleted model: {model_info}")

            except AIModel.DoesNotExist:
                failed_models.append({
                    'id': model_id,
                    'error': '模型不存在'
                })
                logger.warning(f"Model with ID {model_id} does not exist")
            except Exception as e:
                failed_models.append({
                    'id': model_id,
                    'error': str(e)
                })
                logger.error(f"Failed to delete model with ID {model_id}: {str(e)}")

        # 构建响应消息
        success_count = len(deleted_models)
        failed_count = len(failed_models)

        if success_count > 0 and failed_count == 0:
            message = f'成功删除 {success_count} 个模型'
        elif success_count > 0 and failed_count > 0:
            message = f'成功删除 {success_count} 个模型，失败 {failed_count} 个'
        else:
            message = f'删除失败，共 {failed_count} 个模型删除失败'

        return Response({
            'success': True,
            'deleted': deleted_models,
            'failed': failed_models,
            'message': message
        }, status=200)

    except Exception as e:
        logger.error(f"Error deleting models: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'删除模型失败: {str(e)}'
        }, status=500)


@api_view(['PUT'])
@require_admin_auth
def update_model(request, model_id):
    """
    管理员更新模型信息API
    参数: model_id (路径参数), 模型信息 (请求体)
    """
    try:
        # 获取模型实例
        try:
            model = AIModel.objects.get(id=model_id)
        except AIModel.DoesNotExist:
            return Response({
                'success': False,
                'message': f'模型不存在 (ID: {model_id})'
            }, status=404)

        # 验证请求数据
        serializer = AIModelUpdateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': '模型信息验证失败',
                'errors': serializer.errors
            }, status=400)

        validated_data = serializer.validated_data

        # 检查是否存在重复模型（排除当前模型）
        model_name = validated_data['name']
        model_type = validated_data['model_type']
        ocr_role = validated_data.get('ocr_role')
        is_system_model = validated_data.get('is_system_model', False)

        # 构建查询条件
        filter_kwargs = {
            'name': model_name,
            'model_type': model_type,
            'is_system_model': is_system_model
        }

        if model_type == 'ocr':
            filter_kwargs['ocr_role'] = ocr_role

        # 排除当前模型
        existing_model = AIModel.objects.filter(**filter_kwargs).exclude(id=model_id).first()

        if existing_model:
            # 如果存在同名模型，返回详细的错误信息
            if model_type == 'ocr':
                error_msg = f"已存在名为 '{model_name}' 的{model_type}模型（角色: {ocr_role}）"
            else:
                error_msg = f"已存在名为 '{model_name}' 的{model_type}模型"

            return Response({
                'success': False,
                'message': error_msg
            }, status=400)

        # 更新模型信息
        model.name = model_name
        model.model_type = model_type
        model.version = validated_data['version']
        model.description = validated_data.get('description')
        model.ocr_role = ocr_role
        model.is_system_model = is_system_model
        model.ocr_collection_name = validated_data.get('ocr_collection_name')  # 新增OCR集合名称

        model.save()

        logger.info(f"Successfully updated model: {model.name} (ID: {model.id})")

        # 返回更新后的模型信息
        response_serializer = AIModelSerializer(model)
        return Response({
            'success': True,
            'message': '模型信息更新成功',
            'model': response_serializer.data
        }, status=200)

    except Exception as e:
        logger.error(f"Error in update_model: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'更新模型失败: {str(e)}'
        }, status=500)

from django.db import transaction

@api_view(['POST'])
@require_admin_auth
def update_image_order(request):
    """
    更新图片的显示顺序和所属文件夹。
    如果提供了 `folder` 参数，则会将图片移动到该文件夹。
    """
    try:
        data = request.data
        category = data.get('category')
        # `folder` 现在代表目标文件夹
        target_folder = data.get('folder', '').strip('/')
        ordered_image_ids = data.get('ordered_image_ids', [])

        if not category or not isinstance(ordered_image_ids, list):
            return Response({'success': False, 'message': '参数错误: 必须提供 category 和 ordered_image_ids 列表。'}, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            images_to_update = ExampleImage.objects.filter(id__in=ordered_image_ids).select_for_update()
            
            
            id_to_instance_map = {str(image.id): image for image in images_to_update}

            # 确保所有请求的ID都存在
            if len(ordered_image_ids) != len(images_to_update):
                 logger.warning(f"Mismatch in image IDs. Requested: {len(ordered_image_ids)}, Found: {len(images_to_update)}.")
                 # 可以选择返回错误或继续处理找到的图片
                 # return Response({'success': False, 'message': '一个或多个图片ID无效。'}, status=status.HTTP_404_NOT_FOUND)

            updated_count = 0
            for index, image_id_str in enumerate(ordered_image_ids):
                image_id = int(image_id_str)
                image = id_to_instance_map.get(str(image_id))
                
                if not image:
                    logger.warning(f"Image with ID {image_id} not found in queryset during update loop. Skipping.")
                    continue

                # 更新 display_order
                image.display_order = index

                # 更新 path (移动到新文件夹)
                new_path = f"{target_folder}/{image.name}" if target_folder else image.name
                if image.path != new_path:
                    # 检查目标路径是否已存在 (在同一个category下)
                    if ExampleImage.objects.filter(category=image.category, path=new_path).exclude(id=image.id).exists():
                         logger.warning(f"Cannot move image ID {image.id}: target path '{new_path}' already exists.")
                         # 在事务中，如果一个失败，可以选择整体回滚
                         raise Exception(f"目标路径 '{new_path}' 已被另一张图片占用。")
                    
                    # 物理移动文件
                    example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', os.path.join(settings.BASE_DIR, 'models', 'example_images'))
                    old_file_path = os.path.join(example_images_root, image.category, image.path)
                    new_file_path = os.path.join(example_images_root, image.category, new_path)

                    if os.path.exists(old_file_path):
                         # 确保目标目录存在
                         os.makedirs(os.path.dirname(new_file_path), exist_ok=True)
                         os.rename(old_file_path, new_file_path)
                         logger.info(f"Moved file from {old_file_path} to {new_file_path}")
                    else:
                         logger.warning(f"Source file not found for move: {old_file_path}")

                    image.path = new_path
                
                image.save()
                updated_count += 1

        logger.info(f"Successfully updated order and path for {updated_count} images in category='{category}', target_folder='{target_folder}'.")
        return Response({'success': True, 'message': '图片顺序和位置更新成功'}, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error updating image order/path: {str(e)}", exc_info=True)
        return Response({'success': False, 'message': f'更新失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@require_admin_auth
def update_example_image_description(request, image_id):
    """
    更新单个示例图片的描述信息。
    """
    try:
        data = request.data
        description = data.get('description', '')

        try:
            image_instance = ExampleImage.objects.get(id=image_id)
        except ExampleImage.DoesNotExist:
            logger.warning(f"Attempted to update description for non-existent ExampleImage with ID {image_id}")
            return Response({'success': False, 'message': f'ID为 {image_id} 的图片未找到。'}, status=status.HTTP_404_NOT_FOUND)

        image_instance.description = description
        image_instance.save(update_fields=['description'])

        logger.info(f"Successfully updated description for ExampleImage with ID {image_id}")
        return Response({
            'success': True,
            'message': '图片描述更新成功。',
            'image': {
                'id': image_instance.id,
                'description': image_instance.description
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error updating example image description: {str(e)}", exc_info=True)
        return Response({'success': False, 'message': f'更新图片描述失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
@api_view(['POST'])
@require_admin_auth
def create_example_folder(request):
    """
    为示例图片创建一个新的文件夹。
    这实际上是在文件系统上创建一个真实的目录。
    """
    try:
        data = request.data
        category = data.get('category')
        folder_name = data.get('folder_name', '').strip()

        if not category or not folder_name:
            return Response({'success': False, 'message': '必须提供 category 和 folder_name。'}, status=status.HTTP_400_BAD_REQUEST)

        # 验证 category
        allowed_categories = {'barcode', 'ocr', 'ai_restored'}
        if category not in allowed_categories:
            return Response({'success': False, 'message': f'无效的分类: {category}'}, status=status.HTTP_400_BAD_REQUEST)

        # 安全性检查：防止路径遍历
        if '/' in folder_name or '\\' in folder_name or '..' in folder_name:
            return Response({'success': False, 'message': '文件夹名称不能包含斜杠或点。'}, status=status.HTTP_400_BAD_REQUEST)

        # 定义示例图片根目录
        example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', os.path.join(settings.BASE_DIR, 'models', 'example_images'))
        category_dir = os.path.join(example_images_root, category)
        folder_path = os.path.join(category_dir, folder_name)

        if os.path.exists(folder_path):
            return Response({'success': False, 'message': f"文件夹 '{folder_name}' 已存在于分类 '{category}' 中。"}, status=status.HTTP_409_CONFLICT)

        os.makedirs(folder_path, exist_ok=True)
        logger.info(f"Successfully created folder: {folder_path}")

        return Response({
            'success': True,
            'message': f"文件夹 '{folder_name}' 创建成功。",
            'folder': folder_name
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error creating example folder: {str(e)}", exc_info=True)
        return Response({'success': False, 'message': f'创建文件夹失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def rename_example_folder(request):
    """
    重命名一个文件夹。
    这包括重命名文件系统中的目录，并更新数据库中所有相关图片的path字段。
    """
    try:
        data = request.data
        category = data.get('category')
        old_name = data.get('old_name', '').strip()
        new_name = data.get('new_name', '').strip()

        if not all([category, old_name, new_name]):
            return Response({'success': False, 'message': '必须提供 category, old_name, 和 new_name。'}, status=status.HTTP_400_BAD_REQUEST)

        if old_name == new_name:
            return Response({'success': False, 'message': '新旧文件夹名称不能相同。'}, status=status.HTTP_400_BAD_REQUEST)

        # 安全性检查
        for name in [old_name, new_name]:
            if '/' in name or '\\' in name or '..' in name:
                return Response({'success': False, 'message': '文件夹名称不能包含无效字符。'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 定义路径
        example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', os.path.join(settings.BASE_DIR, 'models', 'example_images'))
        category_dir = os.path.join(example_images_root, category)
        old_folder_path = os.path.join(category_dir, old_name)
        new_folder_path = os.path.join(category_dir, new_name)

        if not os.path.isdir(old_folder_path):
            return Response({'success': False, 'message': f"文件夹 '{old_name}' 不存在。"}, status=status.HTTP_404_NOT_FOUND)

        if os.path.exists(new_folder_path):
            return Response({'success': False, 'message': f"目标文件夹名称 '{new_name}' 已存在。"}, status=status.HTTP_409_CONFLICT)
        
        # 批量更新数据库
        with transaction.atomic():
            # 找到所有属于该文件夹的图片
            images_to_update = ExampleImage.objects.filter(category=category, path__startswith=f"{old_name}/")
            
            # 重命名物理目录
            os.rename(old_folder_path, new_folder_path)
            logger.info(f"Renamed folder from {old_folder_path} to {new_folder_path}")

            # 批量更新数据库中的路径
            update_count = 0
            for image in images_to_update:
                original_path = image.path
                image.path = original_path.replace(f"{old_name}/", f"{new_name}/", 1)
                image.save(update_fields=['path'])
                update_count += 1
            
            logger.info(f"Updated {update_count} image paths in database from folder '{old_name}' to '{new_name}'.")

        return Response({
            'success': True,
            'message': f"文件夹 '{old_name}' 已成功重命名为 '{new_name}'。",
            'updated_image_count': update_count
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error renaming example folder: {str(e)}", exc_info=True)
        # 尝试回滚物理文件系统操作
        if 'new_folder_path' in locals() and 'old_folder_path' in locals() and os.path.exists(new_folder_path) and not os.path.exists(old_folder_path):
            os.rename(new_folder_path, old_folder_path)
            logger.warning(f"Rolled back folder rename from {new_folder_path} to {old_folder_path} due to an error.")
        return Response({'success': False, 'message': f'重命名文件夹失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@require_admin_auth
def delete_example_folder(request):
    """
    删除一个空的文件夹。
    如果文件夹内有图片，则拒绝删除。
    """
    try:
        data = request.data
        category = data.get('category')
        folder_name = data.get('folder_name', '').strip()

        if not category or not folder_name:
            return Response({'success': False, 'message': '必须提供 category 和 folder_name。'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件夹内是否还有图片
        if ExampleImage.objects.filter(category=category, path__startswith=f"{folder_name}/").exists():
            return Response({'success': False, 'message': f"文件夹 '{folder_name}' 不为空，无法删除。请先移动或删除其中的所有图片。"}, status=status.HTTP_400_BAD_REQUEST)

        # 定义路径
        example_images_root = getattr(settings, 'EXAMPLE_IMAGES_ROOT', os.path.join(settings.BASE_DIR, 'models', 'example_images'))
        folder_path = os.path.join(example_images_root, category, folder_name)

        if not os.path.isdir(folder_path):
            return Response({'success': False, 'message': f"文件夹 '{folder_name}' 不存在。"}, status=status.HTTP_404_NOT_FOUND)

        # 删除空目录
        os.rmdir(folder_path)
        logger.info(f"Successfully deleted empty folder: {folder_path}")

        return Response({
            'success': True,
            'message': f"文件夹 '{folder_name}' 已成功删除。"
        }, status=status.HTTP_200_OK)

    except OSError as e:
        # 捕捉到试图删除非空目录的异常
        if "not empty" in str(e).lower():
             return Response({'success': False, 'message': f"文件夹 '{folder_name}' 不为空，无法删除。"}, status=status.HTTP_400_BAD_REQUEST)
        logger.error(f"Error deleting example folder (OSError): {str(e)}", exc_info=True)
        return Response({'success': False, 'message': f'删除文件夹时发生操作系统错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logger.error(f"Error deleting example folder: {str(e)}", exc_info=True)
        return Response({'success': False, 'message': f'删除文件夹失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class FeatureMatchingModelView(APIView):
    """
    基于ONNX模型的特征点匹配API视图。
    """
    parser_classes = (MultiPartParser, FormParser)

    def post(self, request, *args, **kwargs):
        logger.info("FeatureMatchingModelView POST request received.")
        
        try:
            # 1. 获取和验证参数
            template_image_file = request.FILES.get('template_image')
            target_image_file = request.FILES.get('target_image')
            model_id = request.POST.get('model_id')
            template_roi_str = request.POST.get('template_roi')

            if not all([template_image_file, target_image_file, model_id]):
                return Response({'error': '必须提供 template_image, target_image 和 model_id 参数。'}, status=status.HTTP_400_BAD_REQUEST)

            template_roi = None
            if template_roi_str:
                try:
                    template_roi = json.loads(template_roi_str)
                    if not all(k in template_roi for k in ['x', 'y', 'width', 'height']):
                        raise ValueError("ROI 必须包含 x, y, width, height。")
                except (json.JSONDecodeError, ValueError) as e:
                    return Response({'error': f'无效的 template_roi 格式: {e}'}, status=status.HTTP_400_BAD_REQUEST)

            # 2. 获取模型实例和路径
            try:
                model_instance = AIModel.objects.get(id=model_id)
                if model_instance.model_type != 'feature_matching':
                    return Response({'error': f"模型 (ID: {model_id}) 类型不是 'feature_matching'。"}, status=status.HTTP_400_BAD_REQUEST)
                
                if not model_instance.model_file or not hasattr(model_instance.model_file, 'path'):
                    return Response({'error': f"模型 (ID: {model_id}) 没有关联的模型文件。"}, status=status.HTTP_404_NOT_FOUND)
                
                # --- 正确构造模型文件路径 (跨平台兼容) V2 ---
                # 从数据库获取的路径(model_file.name)已包含模型类型子目录, e.g., "feature_matching/superpoint.onnx"
                # 但在Windows上保存时可能包含'\'，在Linux上需要标准化为'/'
                normalized_db_path = model_instance.model_file.name.replace('\\', '/')

                if model_instance.is_system_model:
                    # 系统模型: 直接将根目录与标准化的相对路径拼接
                    model_path = os.path.join(settings.SYSTEM_MODELS_ROOT, normalized_db_path)
                else:
                    # 自定义模型: 直接将根目录与标准化的相对路径拼接
                    model_path = os.path.join(settings.MEDIA_ROOT, normalized_db_path)

                if not os.path.exists(model_path):
                    return Response({'error': f"模型文件在路径 '{model_path}' 未找到。"}, status=status.HTTP_404_NOT_FOUND)

            except AIModel.DoesNotExist:
                return Response({'error': f"ID为 {model_id} 的AI模型未找到。"}, status=status.HTTP_404_NOT_FOUND)

            # 3. 将上传的文件读入OpenCV图像格式
            template_image_bytes = np.frombuffer(template_image_file.read(), np.uint8)
            target_image_bytes = np.frombuffer(target_image_file.read(), np.uint8)
            template_image = cv2.imdecode(template_image_bytes, cv2.IMREAD_COLOR)
            target_image = cv2.imdecode(target_image_bytes, cv2.IMREAD_COLOR)

            if template_image is None or target_image is None:
                return Response({'error': '无法解码一个或两个图像文件。'}, status=status.HTTP_400_BAD_REQUEST)

            # 4. 实例化并执行匹配
            
            # 从请求中解析预测器参数，并设置与Demo一致的默认值
            try:
                predictor_params = {
                    'keypoints_count': int(request.POST.get('keypoints_count', 100)),  # run.py默认值：100
                    'nms_grid_size': int(request.POST.get('nms_grid_size', 4)),  # run.py默认值：4
                    # 注意：run.py中threshold=8 (基于0-255范围)，API中需要归一化为0-1范围
                    'nms_threshold': float(request.POST.get('nms_threshold', 8.0 / 255.0)),  # run.py默认值：8
                    'match_ratio_threshold': float(request.POST.get('match_ratio_threshold', 0.8)),  # run.py默认值：0.8
                    'min_match_count': int(request.POST.get('min_match_count', 4)),  # run.py中min_inliers=4
                    'ransac_threshold': float(request.POST.get('ransac_threshold', 5.0)),  # run.py实际使用：5.0
                }
            except (ValueError, TypeError) as e:
                return Response({"status": "error", "message": f"参数类型错误: {e}"}, status=status.HTTP_400_BAD_REQUEST)

            # 实例化预测器
            predictor = ModelBasedFeatureMatcher(model_path=model_path)
            
            # 调用预测，传入所有参数
            result = predictor.predict(
                template_image=template_image,
                target_image=target_image,
                template_roi=template_roi,
                **predictor_params
            )

            # 5. 返回结果
            response_status = status.HTTP_200_OK if result.get('status') == 'success' else status.HTTP_400_BAD_REQUEST
            return Response(result, status=response_status)

        except Exception as e:
            logger.error(f"模型特征点匹配视图发生意外错误: {str(e)}", exc_info=True)
            return Response({'error': f'处理请求时发生意外错误: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
