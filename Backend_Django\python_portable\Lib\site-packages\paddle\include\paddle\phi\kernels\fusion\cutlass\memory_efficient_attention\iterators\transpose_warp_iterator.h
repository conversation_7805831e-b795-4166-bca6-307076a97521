// Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//  Copyright (c) Facebook, Inc. and its affiliates. All rights reserved.
//
//  This source code is licensed under the BSD license found in the
//  LICENSE file in the root directory of this source tree.

#pragma once

#include "./warp_iterator_from_smem.h"

template <typename WarpIterator>
struct TransposeWarpIterator {
  using Iterator = char;
  static bool constexpr kSupportsTranspose = false;
};

template <
    /// Operand identity
    cutlass::gemm::Operand Operand,
    /// Data type of A elements
    typename Element,
    bool kTranspose>
struct TransposeWarpIterator<
    cutlass::gemm::warp::WarpIteratorFromSmem<Operand, Element, kTranspose>> {
  using Iterator =
      cutlass::gemm::warp::WarpIteratorFromSmem<Operand, Element, !kTranspose>;
  static bool constexpr kSupportsTranspose = true;
};
