// Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include "paddle/phi/common/data_type.h"
#include "paddle/phi/core/kernel_factory.h"
#include "paddle/pir/include/core/attribute.h"
#include "paddle/pir/include/core/attribute_base.h"
#include "paddle/pir/include/core/utils.h"

namespace paddle {
namespace dialect {

struct KernelAttributeStorage : public pir::AttributeStorage {
  using ParamKey = phi::KernelKey;

  explicit KernelAttributeStorage(const ParamKey &key) { kernel_key_ = key; }

  static KernelAttributeStorage *Construct(const ParamKey &key) {
    return new KernelAttributeStorage(key);
  }

  static std::size_t HashValue(const ParamKey &key) {
    auto t = phi::KernelKey::Hash()(key);
    return t;
  }

  bool operator==(const ParamKey &key) const { return kernel_key_ == key; }

  ParamKey GetAsKey() const { return kernel_key_; }

 private:
  phi::KernelKey kernel_key_;
};

}  // namespace dialect
}  // namespace paddle
