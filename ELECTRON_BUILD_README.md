# AI Vision App - Electron 打包指南

将 AI Vision App 打包为独立的 Windows exe 应用程序的简单指南。

## 📋 前置要求

1. **Node.js** (v18.x+): https://nodejs.org/
2. **Python虚拟环境已配置**: `Backend_Django\venv` 目录存在且已安装所有依赖

## 🚀 一键构建（推荐）

1. **确保Python环境已准备**:
   ```bash
   cd Backend_Django
   .\venv\Scripts\activate
   pip install -r requirements.txt
   cd ..
   ```

2. **运行构建脚本**:
   ```bash
   .\build-app.bat
   ```

3. **等待完成**（10-20分钟），结果在 `Frontend\release\` 目录

## 🔧 手动构建

如果自动构建失败，可以手动执行：

```bash
# 1. 创建便携式Python环境
robocopy "Backend_Django\venv" "Backend_Django\python_portable" /E /XD __pycache__ /XF *.pyc *.pyo

# 2. 构建前端和打包
cd Frontend
npm install
npm run electron:build:win
```

## 📁 关键文件

- `Frontend/electron-builder.json` - Electron打包配置
- `Frontend/electron/backend.cjs` - 后端启动脚本
- `build-app.bat` - 一键构建脚本
- `Backend_Django/python_portable/` - 便携式Python环境（构建时创建）

## 🐛 常见问题

1. **Python环境问题**: 确保 `Backend_Django\venv` 存在且已安装所有依赖
2. **内存不足**: 关闭其他程序，确保至少8GB可用内存
3. **磁盘空间**: 确保至少10GB可用空间
4. **权限问题**: 以管理员身份运行构建脚本

## 📦 构建结果

成功构建后，在 `Frontend\release\` 目录会生成：
- `AI Vision App Setup x.x.x.exe` - 安装程序
- `win-unpacked/` - 绿色版本目录

---

**注意**: 首次构建可能需要较长时间，因为需要下载和处理大量的 AI 库文件。后续构建会更快。
