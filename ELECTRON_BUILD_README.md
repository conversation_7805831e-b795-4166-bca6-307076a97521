# AI Vision App - Electron 打包指南

本指南详细说明如何将 AI Vision App 打包为独立的 Windows exe 应用程序。

## 📋 前置要求

### 系统要求
- **操作系统**: Windows 10/11 (x64)
- **内存**: 至少 8GB RAM (推荐 16GB)
- **磁盘空间**: 至少 10GB 可用空间
- **网络**: 稳定的网络连接（用于下载依赖）

### 必需软件
1. **Node.js** (v18.x 或更高版本)
   - 下载地址: https://nodejs.org/
   - 验证安装: `node --version`

2. **Python** (3.10-3.13)
   - 下载地址: https://www.python.org/downloads/
   - 验证安装: `python --version`

3. **Git** (可选，用于版本控制)
   - 下载地址: https://git-scm.com/

## 🚀 快速开始

### 方法一：使用一键构建脚本（推荐）

1. **准备 Python 环境**（如果还没有）:
   ```bash
   cd Backend_Django
   python -m venv venv
   .\venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **运行一键构建**:
   ```bash
   # 返回项目根目录
   cd ..
   
   # 运行构建脚本
   .\build-app.bat
   ```

3. **等待构建完成**（通常需要 10-20 分钟）

4. **查看结果**:
   - 构建产物位于 `Frontend\release\` 目录
   - 安装程序: `AI Vision App Setup x.x.x.exe`

### 方法二：手动分步构建

#### 步骤 1: 准备 Python 环境
```powershell
# 创建便携式 Python 环境
.\scripts\prepare-python-env.ps1
```

#### 步骤 2: 构建前端
```bash
cd Frontend
npm install
npm run build
cd ..
```

#### 步骤 3: 打包 Electron 应用
```bash
cd Frontend
npm run electron:build:win
```

## 📁 项目结构说明

```
web_ai_vision_app/
├── Frontend/                    # 前端代码
│   ├── dist/                   # 前端构建产物
│   ├── electron/               # Electron 主进程代码
│   ├── release/                # 最终打包产物
│   ├── package.json            # 前端依赖配置
│   └── electron-builder.json   # Electron 打包配置
├── Backend_Django/             # 后端代码
│   ├── venv/                   # Python 虚拟环境
│   ├── python_portable/        # 便携式 Python 环境
│   ├── manage.py               # Django 管理脚本
│   └── requirements.txt        # Python 依赖
├── scripts/                    # 构建脚本
│   ├── prepare-python-env.ps1  # Python 环境准备脚本
│   └── build-electron-app.ps1  # 完整构建脚本
└── build-app.bat              # 一键构建脚本
```

## ⚙️ 配置说明

### Electron Builder 配置
- **配置文件**: `Frontend/electron-builder.json`
- **关键配置**:
  - 包含便携式 Python 环境
  - 排除不必要的文件（__pycache__, .pyc 等）
  - NSIS 安装程序配置

### Python 环境配置
- **便携式环境**: `Backend_Django/python_portable/`
- **包含内容**:
  - Python 解释器和标准库
  - 所有项目依赖（Django, PyTorch, PaddlePaddle 等）
  - 大小约 2.3GB

## 🔧 高级选项

### 自定义构建
```powershell
# 清理构建
.\scripts\build-electron-app.ps1 -Clean

# 跳过 Python 环境准备
.\scripts\build-electron-app.ps1 -SkipPython

# 跳过前端构建
.\scripts\build-electron-app.ps1 -SkipFrontend

# 仅打包 Electron
.\scripts\build-electron-app.ps1 -SkipPython -SkipFrontend
```

### 构建其他平台
```powershell
# macOS (需要在 macOS 上运行)
.\scripts\build-electron-app.ps1 -Platform mac

# Linux
.\scripts\build-electron-app.ps1 -Platform linux
```

## 🐛 常见问题

### 1. Python 环境问题
**问题**: `Python interpreter not found`
**解决**: 确保虚拟环境已创建并安装了所有依赖

### 2. 内存不足
**问题**: 构建过程中内存不足
**解决**: 
- 关闭其他应用程序
- 增加虚拟内存
- 使用更高配置的机器

### 3. 磁盘空间不足
**问题**: 构建失败，磁盘空间不足
**解决**: 
- 清理临时文件
- 确保至少有 10GB 可用空间

### 4. 网络问题
**问题**: 下载依赖失败
**解决**: 
- 检查网络连接
- 使用国内镜像源
- 重试构建

### 5. 权限问题
**问题**: 文件访问被拒绝
**解决**: 
- 以管理员身份运行
- 检查文件权限
- 关闭杀毒软件

## 📊 性能优化

### 减少包体积
1. **排除开发依赖**: 确保 `electron-builder.json` 正确配置过滤规则
2. **压缩设置**: 使用 `"compression": "normal"` 平衡速度和大小
3. **清理缓存**: 定期清理 Python 缓存文件

### 提升构建速度
1. **使用 SSD**: 在 SSD 上进行构建
2. **增加内存**: 至少 16GB RAM
3. **并行构建**: 使用多线程复制（robocopy /MT）

## 📝 日志和调试

### 构建日志
- **位置**: 控制台输出
- **详细日志**: 使用 `-Verbose` 参数

### 应用日志
- **开发环境**: 控制台输出
- **生产环境**: `%USERPROFILE%\AppData\Roaming\AI Vision App\app.log`

### 后端日志
- **位置**: `%USERPROFILE%\AppData\Roaming\AI Vision App\backend.log`

## 🚀 部署和分发

### 安装程序
- **文件**: `AI Vision App Setup x.x.x.exe`
- **类型**: NSIS 安装程序
- **特性**: 
  - 用户可选择安装目录
  - 创建桌面快捷方式
  - 创建开始菜单项

### 绿色版本
如需绿色版本，可以直接分发 `win-unpacked` 目录中的文件。

### 自动更新
当前版本不包含自动更新功能。如需此功能，可以集成 `electron-updater`。

## 📞 技术支持

如果遇到问题，请：
1. 查看本文档的常见问题部分
2. 检查构建日志中的错误信息
3. 确保所有前置要求都已满足
4. 联系开发团队获取支持

---

**注意**: 首次构建可能需要较长时间，因为需要下载和处理大量的 AI 库文件。后续构建会更快。
