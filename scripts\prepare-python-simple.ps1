# AI Vision App - Simplified Python Environment Preparation
# This script creates a portable Python environment for Electron packaging

param(
    [switch]$Clean = $false
)

Write-Host "=== AI Vision App - Python Environment Preparation (Simplified) ===" -ForegroundColor Green

$SourceVenv = "Backend_Django\venv"
$TargetDir = "Backend_Django\python_portable"

# Check if running from project root
if (-not (Test-Path "Frontend\package.json")) {
    Write-Error "Please run this script from the project root directory"
    exit 1
}

# Clean target directory if requested
if ($Clean -and (Test-Path $TargetDir)) {
    Write-Host "Cleaning existing portable Python directory..." -ForegroundColor Yellow
    Remove-Item $TargetDir -Recurse -Force
}

# Create target directory
if (-not (Test-Path $TargetDir)) {
    Write-Host "Creating portable Python directory: $TargetDir" -ForegroundColor Blue
    New-Item -ItemType Directory -Path $TargetDir -Force | Out-Null
}

# Check if source venv exists
if (-not (Test-Path $SourceVenv)) {
    Write-Error "Source virtual environment not found: $SourceVenv"
    Write-Host "Please ensure the virtual environment is created and dependencies are installed."
    exit 1
}

Write-Host "Copying Python executable..." -ForegroundColor Blue

# Copy Python executable
$pythonExe = Join-Path $SourceVenv "Scripts\python.exe"
$pythonwExe = Join-Path $SourceVenv "Scripts\pythonw.exe"

if (Test-Path $pythonExe) {
    Copy-Item $pythonExe -Destination $TargetDir -Force
    Write-Host "  ✓ Copied python.exe"
} else {
    Write-Error "Python executable not found: $pythonExe"
    exit 1
}

if (Test-Path $pythonwExe) {
    Copy-Item $pythonwExe -Destination $TargetDir -Force
    Write-Host "  ✓ Copied pythonw.exe"
}

# Find and copy Python DLLs
Write-Host "Copying Python DLLs..." -ForegroundColor Blue

# Get Python home directory from pyvenv.cfg
$pyvenvCfg = Join-Path $SourceVenv "pyvenv.cfg"
$pythonHome = ""

if (Test-Path $pyvenvCfg) {
    $content = Get-Content $pyvenvCfg
    $homeLine = $content | Where-Object { $_ -match "^home\s*=" }
    if ($homeLine) {
        $pythonHome = ($homeLine -split "=")[1].Trim()
        Write-Host "  Python home: $pythonHome" -ForegroundColor Gray
    }
}

# If we can't find Python home, try to find it from the executable
if (-not $pythonHome -or -not (Test-Path $pythonHome)) {
    # Try to find Python installation
    $possiblePaths = @(
        "C:\Python313",
        "C:\Python312", 
        "C:\Python311",
        "C:\Python310",
        "C:\Program Files\Python313",
        "C:\Program Files\Python312",
        "C:\Program Files\Python311",
        "C:\Program Files\Python310",
        "$env:LOCALAPPDATA\Programs\Python\Python313",
        "$env:LOCALAPPDATA\Programs\Python\Python312",
        "$env:LOCALAPPDATA\Programs\Python\Python311",
        "$env:LOCALAPPDATA\Programs\Python\Python310"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path "$path\python.exe") {
            $pythonHome = $path
            Write-Host "  Found Python at: $pythonHome" -ForegroundColor Gray
            break
        }
    }
}

if ($pythonHome -and (Test-Path $pythonHome)) {
    # Copy essential DLLs
    $dllPatterns = @("python*.dll", "vcruntime*.dll", "msvcp*.dll", "api-ms-win*.dll")
    
    foreach ($pattern in $dllPatterns) {
        $files = Get-ChildItem -Path $pythonHome -Filter $pattern -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            try {
                Copy-Item $file.FullName -Destination $TargetDir -Force -ErrorAction SilentlyContinue
                Write-Host "  ✓ Copied $($file.Name)" -ForegroundColor Green
            } catch {
                Write-Host "  ⚠ Could not copy $($file.Name): $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    }
} else {
    Write-Warning "Could not locate Python installation directory. Some DLLs may be missing."
}

Write-Host "Copying site-packages..." -ForegroundColor Blue

# Copy site-packages using robocopy for better performance
$sitePackagesSource = Join-Path $SourceVenv "Lib\site-packages"
$sitePackagesTarget = Join-Path $TargetDir "Lib\site-packages"

if (Test-Path $sitePackagesSource) {
    Write-Host "  This may take several minutes..." -ForegroundColor Yellow
    
    # Create target directory
    New-Item -ItemType Directory -Path $sitePackagesTarget -Force | Out-Null
    
    # Use robocopy for efficient copying
    $robocopyArgs = @(
        "`"$sitePackagesSource`"",
        "`"$sitePackagesTarget`"",
        "/E",           # Copy subdirectories including empty ones
        "/R:2",         # Retry 2 times on failed copies
        "/W:1",         # Wait 1 second between retries
        "/MT:4",        # Multi-threaded copy (4 threads)
        "/XD", "__pycache__",  # Exclude __pycache__ directories
        "/XF", "*.pyc", "*.pyo", "*.pyd.bak"  # Exclude compiled Python files
    )
    
    $robocopyCmd = "robocopy " + ($robocopyArgs -join " ")
    Write-Host "  Running: $robocopyCmd" -ForegroundColor Gray
    
    $result = cmd /c $robocopyCmd
    $exitCode = $LASTEXITCODE
    
    # Robocopy exit codes: 0-7 are success, 8+ are errors
    if ($exitCode -le 7) {
        Write-Host "  ✓ Site-packages copied successfully" -ForegroundColor Green
    } else {
        Write-Warning "Robocopy completed with warnings (exit code: $exitCode)"
        # Continue anyway, as some warnings are acceptable
    }
} else {
    Write-Error "Site-packages directory not found: $sitePackagesSource"
    exit 1
}

Write-Host "Copying Python standard library..." -ForegroundColor Blue

# Copy essential standard library components
$libSource = Join-Path $SourceVenv "Lib"
$libTarget = Join-Path $TargetDir "Lib"

# Ensure Lib directory exists
if (-not (Test-Path $libTarget)) {
    New-Item -ItemType Directory -Path $libTarget -Force | Out-Null
}

# Copy essential standard library files
$essentialItems = @("*.py", "encodings", "collections", "email", "html", "http", "json", "logging", "urllib", "xml")

foreach ($item in $essentialItems) {
    $sourcePath = Join-Path $libSource $item
    $files = Get-ChildItem -Path $sourcePath -ErrorAction SilentlyContinue
    
    foreach ($file in $files) {
        if ($file.PSIsContainer) {
            $targetPath = Join-Path $libTarget $file.Name
            if (-not (Test-Path $targetPath)) {
                Copy-Item $file.FullName -Destination $targetPath -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "  ✓ Copied directory: $($file.Name)" -ForegroundColor Green
            }
        } else {
            Copy-Item $file.FullName -Destination $libTarget -Force -ErrorAction SilentlyContinue
            Write-Host "  ✓ Copied file: $($file.Name)" -ForegroundColor Green
        }
    }
}

Write-Host "Creating configuration files..." -ForegroundColor Blue

# Create pyvenv.cfg
$portablePyvenvCfg = Join-Path $TargetDir "pyvenv.cfg"
$pyvenvContent = @"
home = .
include-system-site-packages = false
version = 3.13.2
executable = python.exe
"@

Set-Content -Path $portablePyvenvCfg -Value $pyvenvContent -Force
Write-Host "  ✓ Created pyvenv.cfg" -ForegroundColor Green

# Create python path configuration
$sitePackagesDir = Join-Path $TargetDir "Lib\site-packages"
if (-not (Test-Path $sitePackagesDir)) {
    New-Item -ItemType Directory -Path $sitePackagesDir -Force | Out-Null
}

$pythonPathPth = Join-Path $sitePackagesDir "python-path.pth"
$pythonPathContent = @"
.
Lib
Lib/site-packages
"@

Set-Content -Path $pythonPathPth -Value $pythonPathContent -Force
Write-Host "  ✓ Created python-path.pth" -ForegroundColor Green

# Calculate and display size
$sizeBytes = (Get-ChildItem -Path $TargetDir -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
$sizeMB = [math]::Round($sizeBytes / 1MB, 2)
$sizeGB = [math]::Round($sizeBytes / 1GB, 2)

Write-Host ""
Write-Host "=== Portable Python Environment Ready ===" -ForegroundColor Green
Write-Host "Location: $TargetDir" -ForegroundColor Blue
Write-Host "Size: $sizeMB MB ($sizeGB GB)" -ForegroundColor Blue

# Verify the portable Python works
$portablePython = Join-Path $TargetDir "python.exe"
if (Test-Path $portablePython) {
    Write-Host "✓ Portable Python executable created successfully" -ForegroundColor Green
} else {
    Write-Error "Failed to create portable Python executable"
    exit 1
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Run the Electron build: npm run electron:build:win"
Write-Host "2. Test the packaged application"
Write-Host ""
