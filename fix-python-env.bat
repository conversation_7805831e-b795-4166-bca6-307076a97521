@echo off
REM Fix Python Portable Environment - Copy Standard Library

echo ========================================
echo Fixing Python Portable Environment
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "Backend_Django\python_portable" (
    echo ERROR: Python portable environment not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo Step 1: Copying Python standard library...
echo This may take a few minutes...

REM Copy Python standard library from system Python
robocopy "C:\Python313\Lib" "Backend_Django\python_portable\Lib" /E /XD site-packages __pycache__ /XF *.pyc *.pyo

if errorlevel 8 (
    echo ERROR: Failed to copy Python standard library
    echo Please ensure C:\Python313 exists and is accessible
    pause
    exit /b 1
)

echo.
echo Step 2: Copying Python DLLs...

REM Copy essential Python DLLs
copy "C:\Python313\python313.dll" "Backend_Django\python_portable\" 2>nul
copy "C:\Python313\python3.dll" "Backend_Django\python_portable\" 2>nul
copy "C:\Python313\vcruntime140.dll" "Backend_Django\python_portable\" 2>nul
copy "C:\Python313\vcruntime140_1.dll" "Backend_Django\python_portable\" 2>nul

echo.
echo Step 3: Copying Python executable to root...

REM Copy python.exe to the root of portable environment for easier access
copy "Backend_Django\python_portable\Scripts\python.exe" "Backend_Django\python_portable\" 2>nul
copy "Backend_Django\python_portable\Scripts\pythonw.exe" "Backend_Django\python_portable\" 2>nul

echo.
echo ========================================
echo Python Environment Fixed
echo ========================================
echo.
echo Testing Python environment...

REM Test the fixed Python environment
"Backend_Django\python_portable\python.exe" --version
if errorlevel 1 (
    echo WARNING: Python test failed
) else (
    echo Python test successful
)

echo.
echo Testing Django import...
"Backend_Django\python_portable\python.exe" -c "import django; print('Django version:', django.VERSION)"
if errorlevel 1 (
    echo WARNING: Django import test failed
) else (
    echo Django import test successful
)

echo.
echo Fix completed. You can now rebuild the Electron app.
pause
