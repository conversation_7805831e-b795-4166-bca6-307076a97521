# This file is distributed under the same license as the Django package.
#
# Translators:
# Azat, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2023-12-04 19:03+0000\n"
"Last-Translator: Azat, 2023\n"
"Language-Team: Uyghur (http://app.transifex.com/django/django/language/ug/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ug\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "ئالىي تاللانما"

msgid "Flat Pages"
msgstr "تەكشى بەتلەر"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"مىسال: “/about/contact/”. باش قىسمى ۋە ئاخىر قىسمىدا سىزىق بولۇشى كېرەك."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"بۇ قىممەت پەقەت ھەرپلەر، سانلار، نۇقتىلار، ئاستى سىزىقلار، سىزىقلار، يانتۇ "
"سىزىقلار ياكى تىلدىلاردىن تۈزۈلگەن بولۇشى كېرەك."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "مىسال: “/about/contact”. باش قىسمىدا سىزىق بولۇشى كېرەك."

msgid "URL is missing a leading slash."
msgstr "URL نىڭ باش قىسمىدا سىزىق يوق."

msgid "URL is missing a trailing slash."
msgstr "URL نىڭ ئاخىر قىسمىدا سىزىق يوق."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "%(site)s تور بېكىتى ئۈچۈن %(url)ss URL ئادرېسلىق تەك بەت مەۋجۇت."

msgid "title"
msgstr "ماۋزۇ"

msgid "content"
msgstr "مەزمۇن"

msgid "enable comments"
msgstr "ئىنكاسلارنى قوزغىتىڭ"

msgid "template name"
msgstr "قېلىپ ئاتى"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"مىسال: “flatpages/contact_page.html”. ئەگەر بۇ تەمىنلەنمىسە، سىستېما "
"“flatpages/default.html” نى ئىشلىتىدۇ."

msgid "registration required"
msgstr "تىزىملىتىش زۆرۈر"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "ئەگەر بۇ تاللانسا، پەقەت كىرگەن ئىشلەتكۈچىلەر بەتنى كۆرەلەيدۇ."

msgid "sites"
msgstr "بېكەتلەر"

msgid "flat page"
msgstr "تەكشى بەت"

msgid "flat pages"
msgstr "تەكشى بەتلەر"
