// Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once
#include "paddle/pir/include/dialect/shape/utils/shape_analysis.h"

namespace cinn::dialect {
OP_DECLARE_INFER_SYMBOLIC_SHAPE(Broadcast)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(Concat)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(Pool2d)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(ReduceMax)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(ReduceMin)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(ReduceProd)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(ReduceSum)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(Reshape)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(Slice)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(Split)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(UniformRandom)
OP_DECLARE_INFER_SYMBOLIC_SHAPE(Gather)
}  // namespace cinn::dialect
