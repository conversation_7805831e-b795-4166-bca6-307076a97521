# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Swahili (http://www.transifex.com/django/django/language/"
"sw/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sw\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr "Weka kibinadamu"

msgid "th"
msgstr " "

msgid "st"
msgstr " "

msgid "nd"
msgstr " "

msgid "rd"
msgstr " "

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "milioni %(value).1f"
msgstr[1] "milioni %(value).1f"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "milioni %(value)s"
msgstr[1] "milioni %(value)s"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "bilioni %(value).1f"
msgstr[1] "bilioni %(value).1f"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "bilioni %(value)s"
msgstr[1] "bilioni %(value)s"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "trilioni %(value).1f"
msgstr[1] "trilioni %(value).1f"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "trilioni %(value)s"
msgstr[1] "trilioni %(value)s"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] "kuadrilioni %(value).1f"
msgstr[1] "kuadrilioni %(value).1f"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "kuadrilioni %(value)s"
msgstr[1] "kuadrilioni %(value)s"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] "kuintilioni %(value).1f"
msgstr[1] "kuintilioni %(value).1f"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "kuintilioni %(value)s"
msgstr[1] "kuintilioni %(value)s"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] "seksitilioni %(value).1f"
msgstr[1] "seksitilioni %(value).1f"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "seksitilioni %(value)s"
msgstr[1] "seksitilioni %(value)s"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] "septilioni %(value).1f"
msgstr[1] "septilioni %(value).1f"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "septilioni %(value)s"
msgstr[1] "septilioni %(value)s"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] "%(value).1f oktilioni"
msgstr[1] "%(value).1f oktilioni"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "oktilioni %(value)s"
msgstr[1] "oktilioni %(value)s"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] "nonilioni %(value).1f"
msgstr[1] "nonilioni %(value).1f"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "nonilioni %(value)s"
msgstr[1] "nonilioni %(value)s"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] "desilioni %(value).1f"
msgstr[1] "desilioni %(value).1f"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "desilioni %(value)s"
msgstr[1] "desilioni %(value)s"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] "gogoli %(value).1f"
msgstr[1] "gogoli %(value).1f"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "gogoli %(value)s"
msgstr[1] "gogoli %(value)s"

msgid "one"
msgstr "moja"

msgid "two"
msgstr "mbili"

msgid "three"
msgstr "tatu"

msgid "four"
msgstr "nne"

msgid "five"
msgstr "tano"

msgid "six"
msgstr "sita"

msgid "seven"
msgstr "saba"

msgid "eight"
msgstr "nane"

msgid "nine"
msgstr "tisa"

msgid "today"
msgstr "leo"

msgid "tomorrow"
msgstr "kesho"

msgid "yesterday"
msgstr "jana"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s ago"
msgstr "%(delta)s zilizopita"

msgid "now"
msgstr "sasa"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "Sekunde iliyopita"
msgstr[1] "Sekunde %(count)s zilizopita"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "Dakika iliyopita"
msgstr[1] "Dakika %(count)s zilizopita"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "Saa lililopita"
msgstr[1] "Masaa %(count)s yaliyopita"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s from now"
msgstr "%(delta)s kutoka sasa"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "Sekunde moja kutoka sasa"
msgstr[1] "Sekunde %(count)s kutoka sasa"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "Dakika moja kutoka sasa"
msgstr[1] "Dakika %(count)s kutoka sasa"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "Saa limoja kutoka sasa "
msgstr[1] "Masaa %(count)s kutoka sasa"
