#Bind keys for exit (keys only work on empty lines
#disable_readline(True)		#Disable pyreadline3 completely.
from __future__ import absolute_import
debug_output("off")             #"on" saves log info to./pyreadline_debug_log.txt
                                #"on_nologfile" only enables print warning messages
bind_exit_key("Control-d")
bind_exit_key("Control-z")

#Commands for moving	
bind_key("Home",                "beginning_of_line")
bind_key("End",                 "end_of_line")
bind_key("Left",                "backward_char")
bind_key("Control-b",           "backward_char")
bind_key("Right",               "forward_char")
bind_key("Control-f",           "forward_char")
bind_key("Alt-f",               "forward_word")
bind_key("Alt-b",               "backward_word")
bind_key("Clear",               "clear_screen")
bind_key("Control-l",           "clear_screen")
bind_key("Control-a",           "beginning_of_line")
bind_key("Control-e",           "end_of_line")
#bind_key("Control-l",          "redraw_current_line")

#Commands for Manipulating the History
bind_key("Return",              "accept_line")
bind_key("Control-p",           "previous_history")
bind_key("Control-n",           "next_history")
bind_key("Up",                  "history_search_backward")
bind_key("Down",                "history_search_forward")
bind_key("Alt-<",               "beginning_of_history")
bind_key("Alt->",               "end_of_history")
bind_key("Control-r",           "reverse_search_history")
bind_key("Control-s",           "forward_search_history")
bind_key("Alt-p",               "non_incremental_reverse_search_history")
bind_key("Alt-n",               "non_incremental_forward_search_history")

bind_key("Control-z",           "undo")
bind_key("Control-_",           "undo")

#Commands for Changing Text
bind_key("Delete",              "delete_char")
bind_key("Control-d",           "delete_char")
bind_key("BackSpace",           "backward_delete_char")
#bind_key("Control-Shift-v",    "quoted_insert")
bind_key("Control-space",       "self_insert")
bind_key("Control-BackSpace",   "backward_delete_word")

#Killing and Yanking
bind_key("Control-k",           "kill_line")
bind_key("Control-shift-k",     "kill_whole_line")
bind_key("Escape",              "kill_whole_line")
bind_key("Meta-d",              "kill_word")
bind_key("Control-w",           "unix_word_rubout")
#bind_key("Control-Delete",     "forward_kill_word")

#Copy paste
bind_key("Shift-Right",         "forward_char_extend_selection")
bind_key("Shift-Left",          "backward_char_extend_selection")
bind_key("Shift-Control-Right", "forward_word_extend_selection")
bind_key("Shift-Control-Left",  "backward_word_extend_selection")
bind_key("Control-m",           "set_mark")

bind_key("Control-Shift-x",     "copy_selection_to_clipboard")
#bind_key("Control-c",           "copy_selection_to_clipboard")  #Needs allow_ctrl_c(True) below to be uncommented
bind_key("Control-q",           "copy_region_to_clipboard")
bind_key('Control-Shift-v',     "paste_mulitline_code")
bind_key("Control-x",           "cut_selection_to_clipboard")

bind_key("Control-v",           "paste")
bind_key("Control-y",           "yank")
bind_key("Alt-v",               "ipython_paste")

#Unbinding keys:
#un_bind_key("Home")

#Other
bell_style("none") #modes: none, audible, visible(not implemented)
show_all_if_ambiguous("on")
mark_directories("on")
completer_delims(" \t\n\"\\'`@$><=;|&{(?")
complete_filesystem("off")
debug_output("off")
#allow_ctrl_c(True)  #(Allows use of ctrl-c as copy key, still propagate keyboardinterrupt when not waiting for input)

history_filename("~/.pythonhistory")
history_length(200) #value of -1 means no limit

#set_mode("vi")  #will cause following bind_keys to bind to vi mode as well as activate vi mode
#ctrl_c_tap_time_interval(0.3)
