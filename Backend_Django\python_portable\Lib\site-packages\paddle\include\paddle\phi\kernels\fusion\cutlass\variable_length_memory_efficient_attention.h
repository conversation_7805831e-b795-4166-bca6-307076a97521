// Copyright (c) 2023 PaddlePaddle Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once
#ifdef PADDLE_WITH_MEMORY_EFFICIENT_ATTENTION
#include "./memory_efficient_attention/autogen_variable/cutlass_forward.h"

namespace phi {
namespace fusion {

template <typename T, typename Context>
void MultiHeadAttentionVariableForwardKernel(
    const Context& ctx,
    const DenseTensor& query,
    const DenseTensor& key,
    const DenseTensor& value,
    const DenseTensor& seq_lens,
    const DenseTensor& kv_seq_lens,
    const paddle::optional<DenseTensor>& mask,
    const float scale,
    const bool causal,
    const int pre_cache_length,
    DenseTensor* output);

}  // namespace fusion
}  // namespace phi
#endif  // PADDLE_WITH_MEMORY_EFFICIENT_ATTENTION
