# AI Vision App - Complete Electron Build Script
# This script automates the entire build process for the Electron application

param(
    [switch]$Clean = $false,
    [switch]$SkipPython = $false,
    [switch]$SkipFrontend = $false,
    [switch]$SkipElectron = $false,
    [string]$Platform = "win",
    [switch]$Help = $false
)

if ($Help) {
    Write-Host @"
AI Vision App - Electron Build Script

Usage: .\scripts\build-electron-app.ps1 [OPTIONS]

Options:
  -Clean          Clean all build artifacts before building
  -SkipPython     Skip Python environment preparation
  -SkipFrontend   Skip frontend build
  -SkipElectron   Skip Electron packaging
  -Platform       Target platform (win, mac, linux) [default: win]
  -Help           Show this help message

Examples:
  .\scripts\build-electron-app.ps1                    # Full build
  .\scripts\build-electron-app.ps1 -Clean            # Clean build
  .\scripts\build-electron-app.ps1 -SkipPython       # Skip Python prep
"@
    exit 0
}

Write-Host "=== AI Vision App - Electron Build Process ===" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Blue
Write-Host "Clean: $Clean" -ForegroundColor Blue
Write-Host ""

# Check if running from project root
if (-not (Test-Path "Frontend\package.json")) {
    Write-Error "Please run this script from the project root directory"
    exit 1
}

# Function to check command availability
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Check required tools
Write-Host "Checking required tools..." -ForegroundColor Blue

$requiredTools = @{
    "node" = "Node.js is required for frontend build"
    "npm" = "npm is required for package management"
}

foreach ($tool in $requiredTools.Keys) {
    if (-not (Test-Command $tool)) {
        Write-Error "$($requiredTools[$tool])"
        Write-Host "Please install $tool and ensure it's in your PATH"
        exit 1
    } else {
        Write-Host "  ✓ $tool found" -ForegroundColor Green
    }
}

# Clean build artifacts if requested
if ($Clean) {
    Write-Host "Cleaning build artifacts..." -ForegroundColor Yellow
    
    $cleanPaths = @(
        "Frontend\dist",
        "Frontend\release",
        "Backend_Django\python_portable"
    )
    
    foreach ($cleanPath in $cleanPaths) {
        if (Test-Path $cleanPath) {
            Write-Host "  Removing: $cleanPath" -ForegroundColor Gray
            Remove-Item $cleanPath -Recurse -Force
        }
    }
    Write-Host "  ✓ Clean completed" -ForegroundColor Green
}

# Step 1: Prepare Python Environment
if (-not $SkipPython) {
    Write-Host ""
    Write-Host "Step 1: Preparing Python Environment..." -ForegroundColor Blue
    
    if (-not (Test-Path "Backend_Django\venv")) {
        Write-Error "Python virtual environment not found at Backend_Django\venv"
        Write-Host "Please create and activate the virtual environment first:"
        Write-Host "  cd Backend_Django"
        Write-Host "  python -m venv venv"
        Write-Host "  .\venv\Scripts\activate"
        Write-Host "  pip install -r requirements.txt"
        exit 1
    }
    
    # Try simplified Python preparation script first
    $pythonScripts = @(
        "scripts\prepare-python-simple.ps1",
        "scripts\prepare-python-env.ps1"
    )

    $scriptFound = $false
    foreach ($script in $pythonScripts) {
        if (Test-Path $script) {
            try {
                Write-Host "  Running Python environment preparation: $script" -ForegroundColor Gray
                & $script -Clean:$Clean

                # Check if the portable Python environment was created successfully
                if (Test-Path "Backend_Django\python_portable\python.exe") {
                    Write-Host "  ✓ Python environment prepared successfully" -ForegroundColor Green
                    $scriptFound = $true
                    break
                } else {
                    Write-Warning "Python environment preparation completed but portable Python not found"
                }
            } catch {
                Write-Warning "Python environment preparation failed with $script : $($_.Exception.Message)"
                continue
            }
        }
    }

    if (-not $scriptFound) {
        Write-Error "No working Python preparation script found or all scripts failed"
        exit 1
    }
} else {
    Write-Host "Step 1: Skipping Python environment preparation" -ForegroundColor Yellow
}

# Step 2: Build Frontend
if (-not $SkipFrontend) {
    Write-Host ""
    Write-Host "Step 2: Building Frontend..." -ForegroundColor Blue
    
    Push-Location "Frontend"
    try {
        # Install dependencies if node_modules doesn't exist
        if (-not (Test-Path "node_modules")) {
            Write-Host "  Installing frontend dependencies..." -ForegroundColor Gray
            npm install
            if ($LASTEXITCODE -ne 0) {
                throw "npm install failed"
            }
        }
        
        # Build frontend
        Write-Host "  Building frontend application..." -ForegroundColor Gray
        npm run build
        if ($LASTEXITCODE -ne 0) {
            throw "Frontend build failed"
        }
        
        Write-Host "  ✓ Frontend build completed" -ForegroundColor Green
    } catch {
        Write-Error "Frontend build failed: $($_.Exception.Message)"
        exit 1
    } finally {
        Pop-Location
    }
} else {
    Write-Host "Step 2: Skipping frontend build" -ForegroundColor Yellow
}

# Step 3: Package Electron Application
if (-not $SkipElectron) {
    Write-Host ""
    Write-Host "Step 3: Packaging Electron Application..." -ForegroundColor Blue
    
    Push-Location "Frontend"
    try {
        # Determine build command based on platform
        $buildCommand = switch ($Platform.ToLower()) {
            "win" { "electron:build:win" }
            "mac" { "electron:build:mac" }
            "linux" { "electron:build:linux" }
            default { "electron:build:win" }
        }
        
        Write-Host "  Running: npm run $buildCommand" -ForegroundColor Gray
        Write-Host "  This may take several minutes due to large Python environment..." -ForegroundColor Yellow
        
        npm run $buildCommand
        if ($LASTEXITCODE -ne 0) {
            throw "Electron packaging failed"
        }
        
        Write-Host "  ✓ Electron packaging completed" -ForegroundColor Green
    } catch {
        Write-Error "Electron packaging failed: $($_.Exception.Message)"
        exit 1
    } finally {
        Pop-Location
    }
} else {
    Write-Host "Step 3: Skipping Electron packaging" -ForegroundColor Yellow
}

# Build Summary
Write-Host ""
Write-Host "=== Build Summary ===" -ForegroundColor Green

if (Test-Path "Frontend\release") {
    $releaseFiles = Get-ChildItem "Frontend\release" -File
    Write-Host "Build artifacts created:" -ForegroundColor Blue
    foreach ($file in $releaseFiles) {
        $sizeMB = [math]::Round($file.Length / 1MB, 2)
        Write-Host "  📦 $($file.Name) ($sizeMB MB)" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "✅ Build completed successfully!" -ForegroundColor Green
    Write-Host "📁 Output directory: Frontend\release" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Test the application by running the installer or executable"
    Write-Host "2. Verify that both frontend and backend functionality work correctly"
    Write-Host "3. Test AI model inference features"
    Write-Host ""
} else {
    Write-Warning "No release artifacts found. Build may have failed."
}

Write-Host "Build process completed." -ForegroundColor Green
